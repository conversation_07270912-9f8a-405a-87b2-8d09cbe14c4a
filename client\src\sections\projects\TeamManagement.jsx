import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Avatar,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from '@heroui/react';
import { 
  Users, 
  UserPlus, 
  Mail, 
  MoreVertical, 
  Crown, 
  Shield, 
  User,
  Trash2,
  Edit,
  Send
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const TeamManagement = ({ projectId }) => {
  const { user } = useAuth();
  const [teamMembers, setTeamMembers] = useState([]);
  const [pendingInvitations, setPendingInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  const [isInviting, setIsInviting] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const roles = [
    { key: 'owner', label: 'Owner', icon: Crown, color: 'warning', description: 'Full project control' },
    { key: 'admin', label: 'Admin', icon: Shield, color: 'danger', description: 'Manage team and settings' },
    { key: 'member', label: 'Member', icon: User, color: 'primary', description: 'Contribute to project' },
    { key: 'viewer', label: 'Viewer', icon: User, color: 'default', description: 'View-only access' }
  ];

  useEffect(() => {
    if (projectId) {
      fetchTeamData();
    }
  }, [projectId]);

  const fetchTeamData = async () => {
    try {
      setLoading(true);

      // Fetch team members
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          *,
          profiles:user_id (
            id,
            full_name,
            avatar_url,
            email
          )
        `)
        .eq('project_id', projectId);

      if (membersError) throw membersError;
      setTeamMembers(membersData || []);

      // Fetch pending invitations
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('project_id', projectId)
        .eq('status', 'pending');

      if (invitationsError) throw invitationsError;
      setPendingInvitations(invitationsData || []);

    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error('Failed to load team data');
    } finally {
      setLoading(false);
    }
  };

  const handleInviteMember = async () => {
    if (!inviteEmail || !inviteRole) {
      toast.error('Please fill in all fields');
      return;
    }

    // Check if user is already a member or has pending invitation
    const existingMember = teamMembers.find(member => member.profiles?.email === inviteEmail);
    const existingInvitation = pendingInvitations.find(inv => inv.email === inviteEmail);

    if (existingMember) {
      toast.error('User is already a team member');
      return;
    }

    if (existingInvitation) {
      toast.error('Invitation already sent to this email');
      return;
    }

    setIsInviting(true);

    try {
      const { error } = await supabase
        .from('team_invitations')
        .insert([{
          project_id: projectId,
          email: inviteEmail,
          role: inviteRole,
          invited_by: user.id,
          status: 'pending',
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;

      toast.success('Invitation sent successfully!');
      setInviteEmail('');
      setInviteRole('member');
      onClose();
      fetchTeamData();

    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveMember = async (memberId) => {
    try {
      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Team member removed');
      fetchTeamData();

    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove team member');
    }
  };

  const handleUpdateRole = async (memberId, newRole) => {
    try {
      const { error } = await supabase
        .from('team_members')
        .update({ role: newRole })
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Role updated successfully');
      fetchTeamData();

    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
    }
  };

  const handleCancelInvitation = async (invitationId) => {
    try {
      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;

      toast.success('Invitation cancelled');
      fetchTeamData();

    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast.error('Failed to cancel invitation');
    }
  };

  const getRoleIcon = (role) => {
    const roleObj = roles.find(r => r.key === role);
    return roleObj ? <roleObj.icon size={16} /> : <User size={16} />;
  };

  const getRoleColor = (role) => {
    const roleObj = roles.find(r => r.key === role);
    return roleObj ? roleObj.color : 'default';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const canManageTeam = () => {
    const currentUserMember = teamMembers.find(member => member.user_id === user?.id);
    return currentUserMember && ['owner', 'admin'].includes(currentUserMember.role);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-white/60">Loading team data...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
            <Users size={24} className="text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Team Management</h2>
            <p className="text-white/70">Manage project team members and permissions</p>
          </div>
        </div>
        
        {canManageTeam() && (
          <Button
            color="primary"
            startContent={<UserPlus size={20} />}
            onPress={onOpen}
          >
            Invite Member
          </Button>
        )}
      </div>

      {/* Team Members */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <h3 className="text-lg font-semibold text-white">Team Members</h3>
            <Chip color="primary" variant="flat">
              {teamMembers.length} Members
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {teamMembers.length === 0 ? (
            <div className="text-center py-12">
              <Users size={48} className="text-white/30 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-white mb-2">No Team Members</h4>
              <p className="text-white/60">Invite team members to start collaborating.</p>
            </div>
          ) : (
            <Table
              aria-label="Team members"
              classNames={{
                wrapper: "bg-transparent",
                th: "bg-white/10 text-white",
                td: "text-white/90"
              }}
            >
              <TableHeader>
                <TableColumn>Member</TableColumn>
                <TableColumn>Role</TableColumn>
                <TableColumn>Joined</TableColumn>
                <TableColumn>Actions</TableColumn>
              </TableHeader>
              <TableBody>
                {teamMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={member.profiles?.avatar_url}
                          name={member.profiles?.full_name}
                          size="sm"
                        />
                        <div>
                          <p className="font-medium">{member.profiles?.full_name}</p>
                          <p className="text-sm text-white/60">{member.profiles?.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getRoleColor(member.role)}
                        variant="flat"
                        size="sm"
                        startContent={getRoleIcon(member.role)}
                      >
                        {member.role}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      {formatDate(member.created_at)}
                    </TableCell>
                    <TableCell>
                      {canManageTeam() && member.user_id !== user?.id && (
                        <Dropdown>
                          <DropdownTrigger>
                            <Button
                              size="sm"
                              variant="light"
                              isIconOnly
                            >
                              <MoreVertical size={16} />
                            </Button>
                          </DropdownTrigger>
                          <DropdownMenu>
                            <DropdownItem
                              key="change-role"
                              startContent={<Edit size={16} />}
                            >
                              Change Role
                            </DropdownItem>
                            <DropdownItem
                              key="remove"
                              startContent={<Trash2 size={16} />}
                              color="danger"
                              onPress={() => handleRemoveMember(member.id)}
                            >
                              Remove Member
                            </DropdownItem>
                          </DropdownMenu>
                        </Dropdown>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold text-white">Pending Invitations</h3>
              <Chip color="warning" variant="flat">
                {pendingInvitations.length} Pending
              </Chip>
            </div>
          </CardHeader>
          
          <CardBody className="pt-0">
            <div className="space-y-3">
              {pendingInvitations.map((invitation) => (
                <div
                  key={invitation.id}
                  className="flex items-center justify-between p-3 rounded-lg bg-white/5"
                >
                  <div className="flex items-center gap-3">
                    <Mail size={20} className="text-warning" />
                    <div>
                      <p className="font-medium text-white">{invitation.email}</p>
                      <p className="text-sm text-white/60">
                        Invited as {invitation.role} • {formatDate(invitation.created_at)}
                      </p>
                    </div>
                  </div>
                  
                  {canManageTeam() && (
                    <Button
                      size="sm"
                      color="danger"
                      variant="light"
                      onPress={() => handleCancelInvitation(invitation.id)}
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Invite Member Modal */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        classNames={{
          base: "bg-background/95 backdrop-blur-md",
          header: "border-b border-white/20",
          body: "py-6",
          footer: "border-t border-white/20"
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <h3 className="text-xl font-bold">Invite Team Member</h3>
                <p className="text-white/60 text-sm">Send an invitation to join this project</p>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Email Address"
                    type="email"
                    placeholder="Enter email address"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    variant="bordered"
                    classNames={{
                      input: "text-white",
                      label: "text-white/70"
                    }}
                  />
                  
                  <Select
                    label="Role"
                    placeholder="Select a role"
                    value={inviteRole}
                    onChange={(e) => setInviteRole(e.target.value)}
                    variant="bordered"
                    classNames={{
                      label: "text-white/70",
                      value: "text-white"
                    }}
                  >
                    {roles.filter(role => role.key !== 'owner').map((role) => (
                      <SelectItem key={role.key} value={role.key}>
                        <div className="flex items-center gap-2">
                          <role.icon size={16} />
                          <div>
                            <p className="font-medium">{role.label}</p>
                            <p className="text-xs text-white/60">{role.description}</p>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancel
                </Button>
                <Button
                  color="primary"
                  startContent={<Send size={16} />}
                  onPress={handleInviteMember}
                  isLoading={isInviting}
                >
                  Send Invitation
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </motion.div>
  );
};

export default TeamManagement;
