-- Super Simple Fix - Just create the missing user_integrations table
-- This is the absolute minimum needed to fix the 404 error

-- Create user_integrations table (this is the main one causing 404 errors)
CREATE TABLE IF NOT EXISTS user_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    integration_type TEXT NOT NULL,
    integration_name TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    integration_data JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'pending',
    sync_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, integration_type)
);

-- Create user_activity_logs table if it doesn't exist (for recent activity)
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL,
    event_action TEXT NOT NULL,
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);

-- Enable RLS (required for Supabase)
ALTER TABLE user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

-- Only create policies if they don't exist
DO $$
BEGIN
    -- Policy for user_integrations
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_integrations' AND policyname = 'Users can manage their own integrations') THEN
        CREATE POLICY "Users can manage their own integrations" ON user_integrations
            FOR ALL USING (auth.uid() = user_id);
    END IF;

    -- Policy for user_activity_logs
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_activity_logs' AND policyname = 'Users can view their own activity logs') THEN
        CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_activity_logs' AND policyname = 'Users can insert their own activity logs') THEN
        CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;
END
$$;

-- Function to create essential learning library content (not sample data)
-- This creates default educational content for the platform, not user-specific data
CREATE OR REPLACE FUNCTION create_learning_library_defaults()
RETURNS TEXT AS $$
BEGIN
    -- Only create if learning_content table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'learning_content') THEN
        INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by)
        VALUES
            ('tutorial', 'Platform Getting Started Guide', 'Learn how to use Royaltea platform effectively', 'beginner', 'Platform', true, null),
            ('best_practice', 'Project Collaboration Best Practices', 'Guidelines for effective team collaboration', 'intermediate', 'Collaboration', true, null),
            ('documentation', 'Revenue Sharing Models', 'Understanding different revenue distribution approaches', 'intermediate', 'Business', true, null),
            ('tutorial', 'Task Management Fundamentals', 'How to organize and track your work effectively', 'beginner', 'Productivity', true, null)
        ON CONFLICT DO NOTHING;

        RETURN 'Learning library defaults created successfully';
    ELSE
        RETURN 'Learning content table does not exist - skipped';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_learning_library_defaults() TO authenticated;

-- Comments
COMMENT ON TABLE user_integrations IS 'User integrations with external services - fixes 404 error';
COMMENT ON TABLE user_activity_logs IS 'User navigation and interaction logs for recent activity';
COMMENT ON FUNCTION create_learning_library_defaults IS 'Creates essential learning library content (default educational content, not user sample data)';
