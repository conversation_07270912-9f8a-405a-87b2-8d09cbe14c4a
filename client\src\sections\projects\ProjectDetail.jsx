import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Chip,
  Progress,
  Avatar,
  AvatarGroup,
  Divider,
  Tabs,
  Tab,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell
} from '@heroui/react';
import { 
  FolderOpen, 
  Users, 
  Calendar, 
  Clock, 
  Target, 
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Edit,
  Settings,
  Plus
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const ProjectDetail = ({ projectId }) => {
  const { user } = useAuth();
  const [project, setProject] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (projectId) {
      fetchProjectDetails();
    }
  }, [projectId]);

  const fetchProjectDetails = async () => {
    try {
      setLoading(true);

      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Fetch project tasks
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (tasksError) throw tasksError;
      setTasks(tasksData || []);

      // Fetch team members
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          *,
          profiles:user_id (
            id,
            full_name,
            avatar_url,
            email
          )
        `)
        .eq('project_id', projectId);

      if (membersError) throw membersError;
      setTeamMembers(membersData || []);

    } catch (error) {
      console.error('Error fetching project details:', error);
      toast.error('Failed to load project details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'planning': return 'primary';
      case 'on_hold': return 'warning';
      case 'completed': return 'default';
      case 'cancelled': return 'danger';
      default: return 'default';
    }
  };

  const getTaskStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'warning';
      case 'todo': return 'primary';
      case 'blocked': return 'danger';
      default: return 'default';
    }
  };

  const calculateProgress = () => {
    if (tasks.length === 0) return 0;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    return Math.round((completedTasks / tasks.length) * 100);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTimeRemaining = () => {
    if (!project?.deadline) return null;
    const deadline = new Date(project.deadline);
    const now = new Date();
    const diffTime = deadline - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return { text: 'Overdue', color: 'danger' };
    if (diffDays === 0) return { text: 'Due today', color: 'warning' };
    if (diffDays <= 7) return { text: `${diffDays} days left`, color: 'warning' };
    return { text: `${diffDays} days left`, color: 'success' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-white/60">Loading project details...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FolderOpen size={48} className="text-white/30 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Project Not Found</h3>
          <p className="text-white/60">The requested project could not be found.</p>
        </div>
      </div>
    );
  }

  const timeRemaining = getTimeRemaining();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Project Header */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between w-full">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl bg-primary/20 flex items-center justify-center">
                <FolderOpen size={32} className="text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{project.name}</h1>
                <p className="text-white/70 mb-3">{project.description}</p>
                <div className="flex items-center gap-3">
                  <Chip
                    color={getStatusColor(project.status)}
                    variant="flat"
                    startContent={project.status === 'active' ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                  >
                    {project.status.replace('_', ' ').toUpperCase()}
                  </Chip>
                  {timeRemaining && (
                    <Chip color={timeRemaining.color} variant="flat">
                      <Clock size={14} className="mr-1" />
                      {timeRemaining.text}
                    </Chip>
                  )}
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="flat"
                startContent={<Edit size={16} />}
              >
                Edit
              </Button>
              <Button
                size="sm"
                variant="flat"
                startContent={<Settings size={16} />}
              >
                Settings
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Progress */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">Progress</span>
                <span className="text-white font-semibold">{calculateProgress()}%</span>
              </div>
              <Progress
                value={calculateProgress()}
                color="primary"
                className="w-full"
              />
            </div>

            {/* Team Size */}
            <div className="space-y-2">
              <span className="text-white/70 text-sm">Team Members</span>
              <div className="flex items-center gap-2">
                <Users size={16} className="text-primary" />
                <span className="text-white font-semibold">{teamMembers.length}</span>
                <AvatarGroup size="sm" max={3}>
                  {teamMembers.slice(0, 3).map((member) => (
                    <Avatar
                      key={member.id}
                      src={member.profiles?.avatar_url}
                      name={member.profiles?.full_name}
                      size="sm"
                    />
                  ))}
                </AvatarGroup>
              </div>
            </div>

            {/* Tasks */}
            <div className="space-y-2">
              <span className="text-white/70 text-sm">Tasks</span>
              <div className="flex items-center gap-2">
                <Target size={16} className="text-success" />
                <span className="text-white font-semibold">
                  {tasks.filter(t => t.status === 'completed').length}/{tasks.length}
                </span>
                <span className="text-white/60 text-sm">completed</span>
              </div>
            </div>

            {/* Timeline */}
            <div className="space-y-2">
              <span className="text-white/70 text-sm">Timeline</span>
              <div className="flex items-center gap-2">
                <Calendar size={16} className="text-warning" />
                <div className="text-sm">
                  <div className="text-white font-semibold">
                    {project.start_date ? formatDate(project.start_date) : 'Not set'}
                  </div>
                  <div className="text-white/60">
                    to {project.deadline ? formatDate(project.deadline) : 'No deadline'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Project Tabs */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-6 border-b border-white/20",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-0 h-12",
              tabContent: "group-data-[selected=true]:text-primary text-white/70"
            }}
          >
            <Tab key="overview" title="Overview">
              <div className="p-6 space-y-6">
                {/* Project Description */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Project Description</h3>
                  <p className="text-white/80 leading-relaxed">
                    {project.description || 'No detailed description available.'}
                  </p>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-white/5">
                    <CardBody className="p-4 text-center">
                      <TrendingUp size={24} className="text-success mx-auto mb-2" />
                      <p className="text-2xl font-bold text-white">{calculateProgress()}%</p>
                      <p className="text-white/60 text-sm">Completion Rate</p>
                    </CardBody>
                  </Card>
                  
                  <Card className="bg-white/5">
                    <CardBody className="p-4 text-center">
                      <Users size={24} className="text-primary mx-auto mb-2" />
                      <p className="text-2xl font-bold text-white">{teamMembers.length}</p>
                      <p className="text-white/60 text-sm">Team Members</p>
                    </CardBody>
                  </Card>
                  
                  <Card className="bg-white/5">
                    <CardBody className="p-4 text-center">
                      <Target size={24} className="text-warning mx-auto mb-2" />
                      <p className="text-2xl font-bold text-white">{tasks.length}</p>
                      <p className="text-white/60 text-sm">Total Tasks</p>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab key="tasks" title="Tasks">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white">Project Tasks</h3>
                  <Button
                    color="primary"
                    startContent={<Plus size={16} />}
                    size="sm"
                  >
                    Add Task
                  </Button>
                </div>

                {tasks.length === 0 ? (
                  <div className="text-center py-12">
                    <Target size={48} className="text-white/30 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-white mb-2">No Tasks Yet</h4>
                    <p className="text-white/60">Create your first task to get started.</p>
                  </div>
                ) : (
                  <Table
                    aria-label="Project tasks"
                    classNames={{
                      wrapper: "bg-transparent",
                      th: "bg-white/10 text-white",
                      td: "text-white/90"
                    }}
                  >
                    <TableHeader>
                      <TableColumn>Task</TableColumn>
                      <TableColumn>Status</TableColumn>
                      <TableColumn>Assignee</TableColumn>
                      <TableColumn>Due Date</TableColumn>
                      <TableColumn>Priority</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {tasks.map((task) => (
                        <TableRow key={task.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{task.title}</p>
                              <p className="text-sm text-white/60 truncate">{task.description}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Chip
                              color={getTaskStatusColor(task.status)}
                              variant="flat"
                              size="sm"
                            >
                              {task.status.replace('_', ' ')}
                            </Chip>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar size="sm" name={task.assignee_name || 'Unassigned'} />
                              <span className="text-sm">{task.assignee_name || 'Unassigned'}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {task.due_date ? formatDate(task.due_date) : 'No deadline'}
                          </TableCell>
                          <TableCell>
                            <Chip
                              color={task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'default'}
                              variant="flat"
                              size="sm"
                            >
                              {task.priority || 'Normal'}
                            </Chip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </Tab>

            <Tab key="team" title="Team">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white">Team Members</h3>
                  <Button
                    color="primary"
                    startContent={<Plus size={16} />}
                    size="sm"
                  >
                    Add Member
                  </Button>
                </div>

                {teamMembers.length === 0 ? (
                  <div className="text-center py-12">
                    <Users size={48} className="text-white/30 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-white mb-2">No Team Members</h4>
                    <p className="text-white/60">Invite team members to collaborate on this project.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {teamMembers.map((member) => (
                      <Card key={member.id} className="bg-white/5">
                        <CardBody className="p-4">
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={member.profiles?.avatar_url}
                              name={member.profiles?.full_name}
                              size="md"
                            />
                            <div className="flex-1">
                              <p className="font-medium text-white">{member.profiles?.full_name}</p>
                              <p className="text-sm text-white/60">{member.role}</p>
                              <p className="text-xs text-white/50">{member.profiles?.email}</p>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default ProjectDetail;
