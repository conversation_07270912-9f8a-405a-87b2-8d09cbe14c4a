import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  Card,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
  Checkbox,
  Textarea,
  Avatar,
  Chip,
  Divider
} from '@heroui/react';
import { motion } from 'framer-motion';

const TeamContributors = ({ projectData, setProjectData, projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [newContributor, setNewContributor] = useState({
    email: '',
    display_name: '',
    role: '',
    permission_level: 'Contributor',
    is_admin: false,
    address: '',
    state: '',
    county: '',
    title: '',
    is_company: false,
    company_name: '',
    signer_name: '',
    signer_title: ''
  });
  const [batchEmails, setBatchEmails] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Permission levels
  const permissionLevels = [
    { value: 'Owner', label: 'Owner' },
    { value: 'Admin', label: 'Admin' },
    { value: 'Contributor', label: 'Contributor' },
    { value: 'Viewer', label: 'Viewer' }
  ];

  // Fetch contributors if projectId exists
  useEffect(() => {
    const fetchContributors = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        const { data, error } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId);

        if (error) throw error;

        setProjectData({
          ...projectData,
          contributors: data || []
        });
      } catch (error) {
        console.error('Error fetching contributors:', error);
        toast.error('Failed to load contributors');
      } finally {
        setLoading(false);
      }
    };

    fetchContributors();
  }, [projectId]);

  // Search users
  const searchUsers = async (query) => {
    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    try {
      // Search by display name (partial match)
      const { data, error } = await supabase
        .from('users')
        .select('id, email, display_name, avatar_url')
        .ilike('display_name', `%${query}%`)
        .limit(5);

      if (error) throw error;

      setSearchResults(data || []);
      setShowSearchResults(data.length > 0);
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Failed to search users');
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    searchUsers(query);
  };

  // Handle search result click
  const handleSearchResultClick = (user) => {
    setNewContributor({
      ...newContributor,
      email: user.email,
      display_name: user.display_name,
      user_id: user.id
    });
    setSearchQuery('');
    setShowSearchResults(false);
  };

  // Add contributor
  const addContributor = () => {
    // Validate email
    if (!newContributor.email) {
      toast.error('Email is required');
      return;
    }

    // Check if email already exists
    const emailExists = projectData.contributors.some(
      (contributor) => contributor.email === newContributor.email
    );

    if (emailExists) {
      toast.error('This email is already added as a contributor');
      return;
    }

    // Add new contributor
    const contributor = {
      ...newContributor,
      id: null, // Will be set by the database
      project_id: projectId,
      status: 'pending',
      invitation_sent_at: new Date().toISOString()
    };

    setProjectData({
      ...projectData,
      contributors: [...projectData.contributors, contributor]
    });

    // Reset form
    setNewContributor({
      email: '',
      display_name: '',
      role: '',
      permission_level: 'Contributor',
      is_admin: false,
      address: '',
      state: '',
      county: '',
      title: '',
      is_company: false,
      company_name: '',
      signer_name: '',
      signer_title: ''
    });

    toast.success('Contributor added successfully');
  };

  // Remove contributor
  const removeContributor = (index) => {
    const updatedContributors = [...projectData.contributors];
    updatedContributors.splice(index, 1);

    setProjectData({
      ...projectData,
      contributors: updatedContributors
    });

    toast.success('Contributor removed');
  };

  // Update contributor
  const updateContributor = (index, field, value) => {
    const updatedContributors = [...projectData.contributors];
    updatedContributors[index] = {
      ...updatedContributors[index],
      [field]: value
    };

    setProjectData({
      ...projectData,
      contributors: updatedContributors
    });
  };

  // Batch invite
  const handleBatchInvite = () => {
    if (!batchEmails) {
      toast.error('Please enter at least one email');
      return;
    }

    // Split emails by comma, newline, or space
    const emails = batchEmails
      .split(/[,\\n\\s]+/)
      .map((email) => email.trim())
      .filter((email) => email);

    if (emails.length === 0) {
      toast.error('Please enter valid emails');
      return;
    }

    // Add each email as a contributor
    const newContributors = [];
    const existingEmails = [];

    emails.forEach((email) => {
      // Skip if email is invalid
      if (!email.includes('@')) {
        toast.error(`Invalid email: ${email}`);
        return;
      }

      // Skip if email already exists
      if (
        projectData.contributors.some((contributor) => contributor.email === email) ||
        existingEmails.includes(email)
      ) {
        existingEmails.push(email);
        return;
      }

      // Add new contributor
      newContributors.push({
        email,
        display_name: '',
        role: '',
        permission_level: 'Contributor',
        is_admin: false,
        address: '',
        state: '',
        county: '',
        title: '',
        is_company: false,
        company_name: '',
        signer_name: '',
        signer_title: '',
        id: null,
        project_id: projectId,
        status: 'pending',
        invitation_sent_at: new Date().toISOString()
      });
    });

    if (newContributors.length === 0) {
      toast.error('All emails are already added as contributors');
      return;
    }

    setProjectData({
      ...projectData,
      contributors: [...projectData.contributors, ...newContributors]
    });

    // Show success message
    toast.success(`Added ${newContributors.length} contributors`);

    // Show warning for existing emails
    if (existingEmails.length > 0) {
      toast.error(`${existingEmails.length} emails were already added`);
    }

    // Reset batch emails
    setBatchEmails('');
  };

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-semibold text-foreground mb-2">Team & Contributors</h2>
        <p className="text-default-500 mb-4">
          Add team members and contributors to your project.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="h-fit">
          <CardBody className="p-6">
            <h3 className="text-lg font-medium mb-4">Add Contributor</h3>

            <div className="space-y-4">
              <div className="relative">
                <Input
                  label="Search User"
                  placeholder="Search by name..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  variant="bordered"
                  size="lg"
                />

                {showSearchResults && (
                  <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-y-auto">
                    <CardBody className="p-2">
                      {searchResults.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center gap-3 p-3 rounded-lg hover:bg-default-100 cursor-pointer transition-colors"
                          onClick={() => handleSearchResultClick(user)}
                        >
                          <Avatar
                            src={user.avatar_url || '/default-avatar-specs.png'}
                            alt={`${user.display_name}'s avatar`}
                            size="sm"
                          />
                          <div className="flex-1">
                            <div className="font-medium text-foreground">{user.display_name}</div>
                            <div className="text-sm text-default-500">{user.email}</div>
                          </div>
                        </div>
                      ))}
                    </CardBody>
                  </Card>
                )}
              </div>

              <Input
                label="Email"
                type="email"
                placeholder="Enter email"
                value={newContributor.email}
                onChange={(e) =>
                  setNewContributor({ ...newContributor, email: e.target.value })
                }
                variant="bordered"
                size="lg"
                isRequired
                errorMessage={!newContributor.email ? "Email is required" : ""}
                description="We'll send them an invitation to join the project"
              />

              <Input
                label="Display Name"
                placeholder="Enter display name"
                value={newContributor.display_name}
                onChange={(e) =>
                  setNewContributor({ ...newContributor, display_name: e.target.value })
                }
                variant="bordered"
                size="lg"
              />

              <Checkbox
                isSelected={newContributor.is_company}
                onValueChange={(checked) =>
                  setNewContributor({ ...newContributor, is_company: checked })
                }
              >
                This contributor is a company
              </Checkbox>

              {newContributor.is_company && (
                <>
                  <Input
                    label="Company Name"
                    placeholder="Enter company name"
                    value={newContributor.company_name}
                    onChange={(e) =>
                      setNewContributor({ ...newContributor, company_name: e.target.value })
                    }
                    variant="bordered"
                    size="lg"
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Signer Name"
                      placeholder="Person signing the agreement"
                      value={newContributor.signer_name}
                      onChange={(e) =>
                        setNewContributor({ ...newContributor, signer_name: e.target.value })
                      }
                      variant="bordered"
                      size="lg"
                    />

                    <Input
                      label="Signer Title"
                      placeholder="e.g. CEO, CTO"
                      value={newContributor.signer_title}
                      onChange={(e) =>
                        setNewContributor({ ...newContributor, signer_title: e.target.value })
                      }
                      variant="bordered"
                      size="lg"
                    />
                  </div>
                </>
              )}

              <Input
                label="Address"
                placeholder="Enter address"
                value={newContributor.address}
                onChange={(e) =>
                  setNewContributor({ ...newContributor, address: e.target.value })
                }
                variant="bordered"
                size="lg"
                description="Required for agreement generation."
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="State/Province"
                  placeholder="Enter state/province"
                  value={newContributor.state}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, state: e.target.value })
                  }
                  variant="bordered"
                  size="lg"
                />

                <Input
                  label="County/Region"
                  placeholder="Enter county/region"
                  value={newContributor.county}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, county: e.target.value })
                  }
                  variant="bordered"
                  size="lg"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Role"
                  placeholder="e.g. Developer, Designer"
                  value={newContributor.role}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, role: e.target.value })
                  }
                  variant="bordered"
                  size="lg"
                />

                <Input
                  label="Title"
                  placeholder="e.g. Lead Developer"
                  value={newContributor.title}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, title: e.target.value })
                  }
                  variant="bordered"
                  size="lg"
                />
              </div>

              <Select
                label="Permission Level"
                placeholder="Select permission level"
                selectedKeys={[newContributor.permission_level]}
                onSelectionChange={(keys) =>
                  setNewContributor({
                    ...newContributor,
                    permission_level: Array.from(keys)[0]
                  })
                }
                variant="bordered"
                size="lg"
              >
                {permissionLevels.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    {level.label}
                  </SelectItem>
                ))}
              </Select>

              <Button
                color="primary"
                size="lg"
                onPress={addContributor}
                className="w-full"
              >
                Add Contributor
              </Button>
            </div>
          </CardBody>
        </Card>

        <Card className="mt-6">
          <CardBody className="p-6">
            <h4 className="text-lg font-medium mb-4">Batch Invite</h4>
            <div className="space-y-4">
              <Textarea
                label="Enter multiple emails"
                placeholder="Enter emails separated by commas, spaces, or new lines"
                value={batchEmails}
                onChange={(e) => setBatchEmails(e.target.value)}
                variant="bordered"
                size="lg"
                minRows={3}
                description="All users will be added with Contributor permission level."
              />

              <Button
                color="primary"
                variant="bordered"
                size="lg"
                onPress={handleBatchInvite}
                className="w-full"
              >
                Invite All
              </Button>
            </div>
          </CardBody>
        </Card>

        <Card className="h-fit">
          <CardBody className="p-6">
            <h3 className="text-lg font-medium mb-4">Contributors ({projectData.contributors.length})</h3>

            {projectData.contributors.length === 0 ? (
              <div className="bg-default-100 border border-default-200 rounded-lg p-4 text-center">
                <p className="text-default-500">
                  No contributors added yet. Add team members to collaborate on this project.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {projectData.contributors.map((contributor, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="border border-divider">
                      <CardBody className="p-4">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={contributor.avatar_url || '/default-avatar-specs.png'}
                              alt={`${contributor.display_name || contributor.email}'s avatar`}
                              size="md"
                            />
                            <div>
                              <div className="font-medium text-foreground">
                                {contributor.display_name || 'Unnamed Contributor'}
                              </div>
                              <div className="text-sm text-default-500">{contributor.email}</div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Chip
                              size="sm"
                              color={contributor.status === 'active' ? 'success' : 'warning'}
                              variant="flat"
                            >
                              {contributor.status}
                            </Chip>

                            <Button
                              size="sm"
                              color="danger"
                              variant="light"
                              onPress={() => removeContributor(index)}
                              isDisabled={contributor.email === currentUser?.email}
                              isIconOnly
                            >
                              🗑️
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Input
                            label="Role"
                            placeholder="e.g. Developer, Designer"
                            value={contributor.role || ''}
                            onChange={(e) =>
                              updateContributor(index, 'role', e.target.value)
                            }
                            variant="bordered"
                            size="sm"
                            isDisabled={contributor.email === currentUser?.email}
                          />

                          <Select
                            label="Permission Level"
                            selectedKeys={[contributor.permission_level]}
                            onSelectionChange={(keys) =>
                              updateContributor(index, 'permission_level', Array.from(keys)[0])
                            }
                            variant="bordered"
                            size="sm"
                            isDisabled={contributor.email === currentUser?.email}
                          >
                            {permissionLevels.map((level) => (
                              <SelectItem key={level.value} value={level.value}>
                                {level.label}
                              </SelectItem>
                            ))}
                          </Select>
                        </div>
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default TeamContributors;
