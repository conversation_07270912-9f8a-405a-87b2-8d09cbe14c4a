import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Tabs, Tab, Chip, Badge,
  Progress, Divider, Spinner, Modal, ModalContent, <PERSON>dalHeader, 
  ModalBody, ModalFooter, useDisclosure
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import taskGigIntegrationService from '../../services/taskGigIntegration.service';

/**
 * System Integration Test Component
 * 
 * Comprehensive testing of all system integrations:
 * - Learning ↔ Vetting System
 * - Profile ↔ Learning System  
 * - Friends System
 * - Task ↔ Gig Integration
 * - Studio Contributor System
 */
const SystemIntegrationTest = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [testResults, setTestResults] = useState({});
  const [activeTab, setActiveTab] = useState('overview');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [detailedResults, setDetailedResults] = useState(null);

  useEffect(() => {
    if (currentUser) {
      runAllTests();
    }
  }, [currentUser]);

  const runAllTests = async () => {
    setLoading(true);
    const results = {};

    try {
      // Test Learning-Vetting Integration
      results.learningVetting = await testLearningVettingIntegration();
      
      // Test Profile-Learning Integration
      results.profileLearning = await testProfileLearningIntegration();
      
      // Test Friends System
      results.friendsSystem = await testFriendsSystem();
      
      // Test Task-Gig Integration
      results.taskGigIntegration = await testTaskGigIntegration();
      
      // Test Studio Contributor System
      results.studioContributors = await testStudioContributorSystem();
      
      setTestResults(results);
      
    } catch (error) {
      console.error('Error running integration tests:', error);
      toast.error('Failed to run integration tests');
    } finally {
      setLoading(false);
    }
  };

  const testLearningVettingIntegration = async () => {
    const test = {
      name: 'Learning ↔ Vetting Integration',
      status: 'testing',
      checks: [],
      score: 0
    };

    try {
      // Check if learning progress affects vetting
      const { data: learningProgress, error: learningError } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Learning Progress Table Access',
        passed: !learningError,
        details: learningError ? learningError.message : `Found ${learningProgress?.length || 0} learning records`
      });

      // Check vetting requirements
      const { data: vettingData, error: vettingError } = await supabase
        .from('user_vetting')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Vetting System Access',
        passed: !vettingError,
        details: vettingError ? vettingError.message : `Vetting level: ${vettingData?.[0]?.current_level || 'None'}`
      });

      // Check if learning hours are tracked for vetting
      const totalLearningHours = learningProgress?.reduce((sum, progress) => 
        sum + (progress.time_spent_minutes || 0), 0) / 60;

      test.checks.push({
        name: 'Learning Hours Tracking',
        passed: totalLearningHours >= 0,
        details: `Total learning hours: ${totalLearningHours.toFixed(1)}`
      });

      test.score = (test.checks.filter(c => c.passed).length / test.checks.length) * 100;
      test.status = test.score >= 80 ? 'passed' : test.score >= 50 ? 'warning' : 'failed';

    } catch (error) {
      test.status = 'error';
      test.error = error.message;
    }

    return test;
  };

  const testProfileLearningIntegration = async () => {
    const test = {
      name: 'Profile ↔ Learning Integration',
      status: 'testing',
      checks: [],
      score: 0
    };

    try {
      // Check profile data
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      test.checks.push({
        name: 'Profile Data Access',
        passed: !profileError,
        details: profileError ? profileError.message : 'Profile accessible'
      });

      // Check skills integration
      const { data: skills, error: skillsError } = await supabase
        .from('user_skills')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Skills Integration',
        passed: !skillsError,
        details: skillsError ? skillsError.message : `${skills?.length || 0} skills tracked`
      });

      // Check learning progress display
      const { data: progress, error: progressError } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Learning Progress Display',
        passed: !progressError,
        details: progressError ? progressError.message : `${progress?.length || 0} courses tracked`
      });

      test.score = (test.checks.filter(c => c.passed).length / test.checks.length) * 100;
      test.status = test.score >= 80 ? 'passed' : test.score >= 50 ? 'warning' : 'failed';

    } catch (error) {
      test.status = 'error';
      test.error = error.message;
    }

    return test;
  };

  const testFriendsSystem = async () => {
    const test = {
      name: 'Friends System',
      status: 'testing',
      checks: [],
      score: 0
    };

    try {
      // Check friends table
      const { data: friends, error: friendsError } = await supabase
        .from('user_allies')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Friends Table Access',
        passed: !friendsError,
        details: friendsError ? friendsError.message : `${friends?.length || 0} friends`
      });

      // Check friend requests
      const { data: requests, error: requestsError } = await supabase
        .from('friend_requests')
        .select('*')
        .eq('recipient_id', currentUser.id);

      test.checks.push({
        name: 'Friend Requests System',
        passed: !requestsError,
        details: requestsError ? requestsError.message : `${requests?.length || 0} pending requests`
      });

      // Check messaging system
      const { data: messages, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('sender_id', currentUser.id)
        .limit(1);

      test.checks.push({
        name: 'Messaging System',
        passed: !messagesError,
        details: messagesError ? messagesError.message : 'Messaging accessible'
      });

      test.score = (test.checks.filter(c => c.passed).length / test.checks.length) * 100;
      test.status = test.score >= 80 ? 'passed' : test.score >= 50 ? 'warning' : 'failed';

    } catch (error) {
      test.status = 'error';
      test.error = error.message;
    }

    return test;
  };

  const testTaskGigIntegration = async () => {
    const test = {
      name: 'Task ↔ Gig Integration',
      status: 'testing',
      checks: [],
      score: 0
    };

    try {
      // Check task-gig relationships table
      const { data: relationships, error: relationshipsError } = await supabase
        .from('task_gig_relationships')
        .select('*')
        .eq('converted_by', currentUser.id);

      test.checks.push({
        name: 'Task-Gig Relationships Table',
        passed: !relationshipsError,
        details: relationshipsError ? relationshipsError.message : `${relationships?.length || 0} relationships`
      });

      // Check gig assignments
      const { data: assignments, error: assignmentsError } = await supabase
        .from('gig_task_assignments')
        .select('*')
        .eq('assigned_user_id', currentUser.id);

      test.checks.push({
        name: 'Gig Task Assignments',
        passed: !assignmentsError,
        details: assignmentsError ? assignmentsError.message : `${assignments?.length || 0} assignments`
      });

      // Check conversion functions
      try {
        const stats = await taskGigIntegrationService.getIntegrationStats(currentUser.id);
        test.checks.push({
          name: 'Integration Service Functions',
          passed: true,
          details: `${stats.totalConversions} total conversions`
        });
      } catch (serviceError) {
        test.checks.push({
          name: 'Integration Service Functions',
          passed: false,
          details: serviceError.message
        });
      }

      test.score = (test.checks.filter(c => c.passed).length / test.checks.length) * 100;
      test.status = test.score >= 80 ? 'passed' : test.score >= 50 ? 'warning' : 'failed';

    } catch (error) {
      test.status = 'error';
      test.error = error.message;
    }

    return test;
  };

  const testStudioContributorSystem = async () => {
    const test = {
      name: 'Studio Contributor System',
      status: 'testing',
      checks: [],
      score: 0
    };

    try {
      // Check project contributors
      const { data: contributors, error: contributorsError } = await supabase
        .from('project_contributors')
        .select('*')
        .eq('user_id', currentUser.id);

      test.checks.push({
        name: 'Project Contributors Access',
        passed: !contributorsError,
        details: contributorsError ? contributorsError.message : `Contributing to ${contributors?.length || 0} projects`
      });

      // Check collaboration requests
      const { data: collabRequests, error: collabError } = await supabase
        .from('collaboration_requests')
        .select('*')
        .eq('requester_id', currentUser.id);

      test.checks.push({
        name: 'Collaboration Requests',
        passed: !collabError,
        details: collabError ? collabError.message : `${collabRequests?.length || 0} collaboration requests`
      });

      // Check applications
      const { data: applications, error: applicationsError } = await supabase
        .from('collaboration_request_applications')
        .select('*')
        .eq('applicant_id', currentUser.id);

      test.checks.push({
        name: 'Collaboration Applications',
        passed: !applicationsError,
        details: applicationsError ? applicationsError.message : `${applications?.length || 0} applications submitted`
      });

      test.score = (test.checks.filter(c => c.passed).length / test.checks.length) * 100;
      test.status = test.score >= 80 ? 'passed' : test.score >= 50 ? 'warning' : 'failed';

    } catch (error) {
      test.status = 'error';
      test.error = error.message;
    }

    return test;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'passed': return 'success';
      case 'warning': return 'warning';
      case 'failed': return 'danger';
      case 'error': return 'danger';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed': return 'bi-check-circle';
      case 'warning': return 'bi-exclamation-triangle';
      case 'failed': return 'bi-x-circle';
      case 'error': return 'bi-bug';
      default: return 'bi-clock';
    }
  };

  const showDetails = (test) => {
    setDetailedResults(test);
    onOpen();
  };

  const calculateOverallScore = () => {
    const tests = Object.values(testResults);
    if (tests.length === 0) return 0;
    
    const totalScore = tests.reduce((sum, test) => sum + (test.score || 0), 0);
    return Math.round(totalScore / tests.length);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2">Running integration tests...</span>
      </div>
    );
  }

  return (
    <div className="system-integration-test space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">System Integration Test</h2>
          <p className="text-default-600">Comprehensive testing of all system integrations</p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{calculateOverallScore()}%</div>
            <div className="text-sm text-default-600">Overall Score</div>
          </div>
          <Button color="primary" onClick={runAllTests}>
            <i className="bi bi-arrow-clockwise mr-1"></i>
            Re-run Tests
          </Button>
        </div>
      </div>

      {/* Overall Status */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Integration Status Overview</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(testResults).map(([key, test]) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => showDetails(test)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{test.name}</h4>
                  <i className={`${getStatusIcon(test.status)} text-${getStatusColor(test.status)}`}></i>
                </div>
                
                <div className="space-y-2">
                  <Progress
                    value={test.score || 0}
                    color={getStatusColor(test.status)}
                    size="sm"
                  />
                  
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-default-600">
                      {test.checks?.filter(c => c.passed).length || 0}/{test.checks?.length || 0} checks passed
                    </span>
                    <Chip
                      size="sm"
                      color={getStatusColor(test.status)}
                      variant="flat"
                    >
                      {test.status}
                    </Chip>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Detailed Results */}
      <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab}>
        <Tab key="overview" title="Overview">
          <div className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Integration Summary</h3>
              </CardHeader>
              <CardBody>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-success">
                      {Object.values(testResults).filter(t => t.status === 'passed').length}
                    </div>
                    <div className="text-sm text-default-600">Passed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-warning">
                      {Object.values(testResults).filter(t => t.status === 'warning').length}
                    </div>
                    <div className="text-sm text-default-600">Warnings</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-danger">
                      {Object.values(testResults).filter(t => t.status === 'failed' || t.status === 'error').length}
                    </div>
                    <div className="text-sm text-default-600">Failed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">
                      {Object.values(testResults).reduce((sum, t) => sum + (t.checks?.length || 0), 0)}
                    </div>
                    <div className="text-sm text-default-600">Total Checks</div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </Tab>
        
        <Tab key="recommendations" title="Recommendations">
          <div className="mt-4 space-y-4">
            {Object.values(testResults)
              .filter(test => test.status !== 'passed')
              .map((test, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <i className={`${getStatusIcon(test.status)} text-${getStatusColor(test.status)}`}></i>
                      <h4 className="font-semibold">{test.name}</h4>
                    </div>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-2">
                      {test.checks?.filter(check => !check.passed).map((check, checkIndex) => (
                        <div key={checkIndex} className="p-2 bg-danger/10 rounded border-l-4 border-danger">
                          <div className="font-medium text-sm">{check.name}</div>
                          <div className="text-xs text-default-600">{check.details}</div>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>
              ))}
          </div>
        </Tab>
      </Tabs>

      {/* Detailed Results Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">{detailedResults?.name} - Detailed Results</h3>
          </ModalHeader>
          
          <ModalBody>
            {detailedResults && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Chip
                    color={getStatusColor(detailedResults.status)}
                    variant="flat"
                    startContent={<i className={getStatusIcon(detailedResults.status)}></i>}
                  >
                    {detailedResults.status.toUpperCase()}
                  </Chip>
                  <div className="text-right">
                    <div className="text-lg font-bold">{detailedResults.score}%</div>
                    <div className="text-sm text-default-600">Score</div>
                  </div>
                </div>

                <Progress
                  value={detailedResults.score || 0}
                  color={getStatusColor(detailedResults.status)}
                />

                <Divider />

                <div className="space-y-3">
                  <h4 className="font-semibold">Individual Checks:</h4>
                  {detailedResults.checks?.map((check, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded border-l-4 ${
                        check.passed 
                          ? 'bg-success/10 border-success' 
                          : 'bg-danger/10 border-danger'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{check.name}</span>
                        <i className={`${check.passed ? 'bi-check-circle text-success' : 'bi-x-circle text-danger'}`}></i>
                      </div>
                      <p className="text-sm text-default-600">{check.details}</p>
                    </div>
                  ))}
                </div>

                {detailedResults.error && (
                  <div className="p-3 bg-danger/10 rounded border border-danger">
                    <h5 className="font-medium text-danger mb-1">Error Details:</h5>
                    <p className="text-sm">{detailedResults.error}</p>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SystemIntegrationTest;
