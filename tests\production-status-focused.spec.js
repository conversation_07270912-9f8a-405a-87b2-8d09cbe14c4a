/**
 * FOCUSED PRODUCTION STATUS ASSESSMENT
 * 
 * Simplified test to get clear status on key user flows
 * Test Credentials: <EMAIL> / TestPassword123!
 */

import { test, expect } from '@playwright/test';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Production Status Assessment', () => {
  let page;
  let context;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    page = await context.newPage();
  });

  test.afterAll(async () => {
    await context.close();
  });

  test('Authentication Flow Assessment', async () => {
    console.log('\n🔐 Testing Authentication Flow...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of initial state
    await page.screenshot({ path: 'test-results/auth-initial.png', fullPage: true });
    
    // Look for email and password inputs
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
    
    // Check if login form is visible
    const emailVisible = await emailInput.isVisible();
    const passwordVisible = await passwordInput.isVisible();
    
    console.log(`📧 Email input visible: ${emailVisible}`);
    console.log(`🔒 Password input visible: ${passwordVisible}`);
    
    if (emailVisible && passwordVisible) {
      // Fill credentials
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      
      // Look for submit button - be more specific
      const submitButton = page.locator('button[type="submit"]').first();
      
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Take screenshot after login attempt
        await page.screenshot({ path: 'test-results/auth-post-login.png', fullPage: true });
        
        // Check for successful authentication indicators
        const dashboardElements = [
          'text=Dashboard',
          'text=Start',
          'text=Track', 
          'text=Earn'
        ];
        
        let authenticated = false;
        for (const selector of dashboardElements) {
          if (await page.locator(selector).isVisible()) {
            authenticated = true;
            console.log(`✅ Found dashboard element: ${selector}`);
            break;
          }
        }
        
        console.log(`🎯 Authentication Status: ${authenticated ? 'SUCCESS' : 'FAILED'}`);
      } else {
        console.log('❌ Submit button not found');
      }
    } else {
      console.log('❌ Login form not found');
    }
  });

  test('Main Navigation Assessment', async () => {
    console.log('\n🧭 Testing Main Navigation...');
    
    const pages = [
      { url: '/#/', name: 'Dashboard' },
      { url: '/#/start', name: 'Start (Studios)' },
      { url: '/#/track', name: 'Track (Projects)' },
      { url: '/#/earn', name: 'Earn (Gigwork)' }
    ];
    
    for (const pageInfo of pages) {
      try {
        await page.goto(`${PRODUCTION_URL}${pageInfo.url}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const screenshot = `test-results/nav-${pageInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.png`;
        await page.screenshot({ path: screenshot, fullPage: true });
        
        // Check for page-specific content
        const pageContent = await page.textContent('body');
        const hasContent = pageContent && pageContent.length > 100;
        
        console.log(`📄 ${pageInfo.name}: ${hasContent ? 'HAS CONTENT' : 'MINIMAL CONTENT'}`);
        
        // Look for key functionality indicators
        if (pageInfo.name.includes('Start')) {
          const studioElements = await page.locator('text=studio, text=Studio, button:has-text("Create")').count();
          console.log(`   🏢 Studio elements found: ${studioElements}`);
        }
        
        if (pageInfo.name.includes('Track')) {
          const projectElements = await page.locator('text=project, text=Project, .kanban, .task').count();
          console.log(`   📋 Project/Task elements found: ${projectElements}`);
        }
        
        if (pageInfo.name.includes('Earn')) {
          const gigElements = await page.locator('text=gig, text=Gig, text=mission, text=Mission').count();
          console.log(`   💼 Gig elements found: ${gigElements}`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
  });

  test('UI Quality Quick Check', async () => {
    console.log('\n🎨 Testing UI Quality...');
    
    const pages = ['/#/', '/#/start', '/#/track', '/#/earn'];
    
    for (const pagePath of pages) {
      try {
        await page.goto(`${PRODUCTION_URL}${pagePath}`);
        await page.waitForLoadState('networkidle');
        
        // Check for horizontal overflow
        const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
        const viewportWidth = await page.evaluate(() => window.innerWidth);
        const hasOverflow = bodyWidth > viewportWidth + 10;
        
        // Check for placeholder content
        const placeholderCount = await page.locator('text=Lorem ipsum, text=placeholder, text=TODO, text=Coming soon').count();
        
        // Check for broken images
        const images = await page.locator('img').all();
        let brokenImages = 0;
        for (const img of images) {
          const naturalWidth = await img.evaluate(el => el.naturalWidth);
          if (naturalWidth === 0) brokenImages++;
        }
        
        console.log(`📱 Page ${pagePath}:`);
        console.log(`   Overflow: ${hasOverflow ? 'YES' : 'NO'}`);
        console.log(`   Placeholders: ${placeholderCount}`);
        console.log(`   Broken images: ${brokenImages}`);
        
      } catch (error) {
        console.log(`❌ UI check error for ${pagePath}: ${error.message}`);
      }
    }
  });

  test('Payment Integration Check', async () => {
    console.log('\n💳 Testing Payment Integration...');
    
    const paymentPages = [
      '/#/revenue',
      '/#/payments', 
      '/#/royalty'
    ];
    
    let paymentSystemFound = false;
    
    for (const pagePath of paymentPages) {
      try {
        await page.goto(`${PRODUCTION_URL}${pagePath}`);
        await page.waitForLoadState('networkidle');
        
        const paymentContent = await page.locator('text=Teller, text=payment, text=Payment, text=revenue, text=Revenue, text=bank, text=Bank').count();
        
        if (paymentContent > 0) {
          paymentSystemFound = true;
          console.log(`💰 Payment content found on ${pagePath}: ${paymentContent} elements`);
          
          await page.screenshot({ path: `test-results/payment-${pagePath.replace(/[^a-z0-9]/g, '-')}.png`, fullPage: true });
        }
        
      } catch (error) {
        console.log(`⚠️ Error checking ${pagePath}: ${error.message}`);
      }
    }
    
    console.log(`🎯 Payment System Status: ${paymentSystemFound ? 'FOUND' : 'NOT FOUND'}`);
  });

  test('Database Connectivity Check', async () => {
    console.log('\n🗄️ Testing Database Connectivity...');
    
    // Check if data is loading by looking for loading states or actual content
    await page.goto(`${PRODUCTION_URL}/#/track`);
    await page.waitForLoadState('networkidle');
    
    // Look for signs of data loading
    const loadingIndicators = await page.locator('.loading, .spinner, text=Loading').count();
    const dataElements = await page.locator('.project, .task, .studio, .card').count();
    
    console.log(`⏳ Loading indicators: ${loadingIndicators}`);
    console.log(`📊 Data elements: ${dataElements}`);
    
    // Check console for database errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error' && msg.text().includes('supabase')) {
        logs.push(msg.text());
      }
    });
    
    await page.waitForTimeout(3000);
    
    if (logs.length > 0) {
      console.log('🚨 Database errors found:');
      logs.forEach(log => console.log(`   ${log}`));
    } else {
      console.log('✅ No obvious database errors');
    }
  });

});
