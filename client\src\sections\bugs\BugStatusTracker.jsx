import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Progress,
  Chip,
  Divider,
  Button,
  Select,
  SelectItem
} from '@heroui/react';
import { 
  Bug, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  XCircle,
  BarChart3,
  Filter
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { supabase } from '../../utils/supabase/supabase.utils';

const BugStatusTracker = () => {
  const [bugStats, setBugStats] = useState({
    total: 0,
    open: 0,
    in_progress: 0,
    resolved: 0,
    closed: 0
  });
  const [recentBugs, setRecentBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('7');

  useEffect(() => {
    fetchBugData();
  }, [timeFilter]);

  const fetchBugData = async () => {
    try {
      setLoading(true);
      
      // Calculate date range
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(timeFilter));
      
      // Fetch all bugs for stats
      const { data: allBugs, error: statsError } = await supabase
        .from('bug_reports')
        .select('status, severity, created_at');

      if (statsError) throw statsError;

      // Calculate statistics
      const stats = {
        total: allBugs?.length || 0,
        open: allBugs?.filter(bug => bug.status === 'open').length || 0,
        in_progress: allBugs?.filter(bug => bug.status === 'in_progress').length || 0,
        resolved: allBugs?.filter(bug => bug.status === 'resolved').length || 0,
        closed: allBugs?.filter(bug => bug.status === 'closed').length || 0
      };

      setBugStats(stats);

      // Fetch recent bugs within time filter
      const { data: recentData, error: recentError } = await supabase
        .from('bug_reports')
        .select('*')
        .gte('created_at', daysAgo.toISOString())
        .order('created_at', { ascending: false })
        .limit(10);

      if (recentError) throw recentError;
      setRecentBugs(recentData || []);

    } catch (error) {
      console.error('Error fetching bug data:', error);
      toast.error('Failed to load bug tracking data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'danger';
      case 'in_progress': return 'warning';
      case 'resolved': return 'success';
      case 'closed': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return <AlertTriangle size={16} />;
      case 'in_progress': return <Clock size={16} />;
      case 'resolved': return <CheckCircle size={16} />;
      case 'closed': return <XCircle size={16} />;
      default: return <Bug size={16} />;
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'danger';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getResolutionRate = () => {
    if (bugStats.total === 0) return 0;
    return Math.round(((bugStats.resolved + bugStats.closed) / bugStats.total) * 100);
  };

  const timeFilterOptions = [
    { key: '7', label: 'Last 7 days' },
    { key: '30', label: 'Last 30 days' },
    { key: '90', label: 'Last 3 months' },
    { key: '365', label: 'Last year' }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
            <BarChart3 size={24} className="text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Bug Status Tracker</h2>
            <p className="text-white/70">Monitor bug resolution progress and trends</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Filter size={20} className="text-white/60" />
          <Select
            size="sm"
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
            className="w-40"
            variant="bordered"
            classNames={{
              value: "text-white"
            }}
          >
            {timeFilterOptions.map((option) => (
              <SelectItem key={option.key} value={option.key}>
                {option.label}
              </SelectItem>
            ))}
          </Select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Total Bugs</p>
                <p className="text-2xl font-bold text-white">{bugStats.total}</p>
              </div>
              <Bug size={24} className="text-white/60" />
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Open Issues</p>
                <p className="text-2xl font-bold text-danger">{bugStats.open}</p>
              </div>
              <AlertTriangle size={24} className="text-danger" />
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">In Progress</p>
                <p className="text-2xl font-bold text-warning">{bugStats.in_progress}</p>
              </div>
              <Clock size={24} className="text-warning" />
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Resolved</p>
                <p className="text-2xl font-bold text-success">{bugStats.resolved}</p>
              </div>
              <CheckCircle size={24} className="text-success" />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Resolution Progress */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <TrendingUp size={20} className="text-success" />
            <h3 className="text-lg font-semibold text-white">Resolution Progress</h3>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white/70">Overall Resolution Rate</span>
              <span className="text-white font-semibold">{getResolutionRate()}%</span>
            </div>
            <Progress
              value={getResolutionRate()}
              color="success"
              className="w-full"
              classNames={{
                track: "bg-white/20",
                indicator: "bg-gradient-to-r from-success-400 to-success-600"
              }}
            />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <p className="text-danger font-medium">{bugStats.open}</p>
                <p className="text-white/60">Open</p>
              </div>
              <div className="text-center">
                <p className="text-warning font-medium">{bugStats.in_progress}</p>
                <p className="text-white/60">In Progress</p>
              </div>
              <div className="text-center">
                <p className="text-success font-medium">{bugStats.resolved}</p>
                <p className="text-white/60">Resolved</p>
              </div>
              <div className="text-center">
                <p className="text-white/80 font-medium">{bugStats.closed}</p>
                <p className="text-white/60">Closed</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Recent Bug Reports */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold text-white">Recent Bug Reports</h3>
        </CardHeader>
        <CardBody className="pt-0">
          {recentBugs.length === 0 ? (
            <div className="text-center py-8">
              <Bug size={48} className="text-white/30 mx-auto mb-4" />
              <p className="text-white/60">No recent bug reports in the selected time period</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentBugs.map((bug, index) => (
                <motion.div
                  key={bug.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-1">
                      <h4 className="font-medium text-white truncate">{bug.title}</h4>
                      <div className="flex gap-2">
                        <Chip
                          size="sm"
                          color={getSeverityColor(bug.severity)}
                          variant="flat"
                        >
                          {bug.severity}
                        </Chip>
                        <Chip
                          size="sm"
                          color={getStatusColor(bug.status)}
                          variant="flat"
                          startContent={getStatusIcon(bug.status)}
                        >
                          {bug.status.replace('_', ' ')}
                        </Chip>
                      </div>
                    </div>
                    <p className="text-sm text-white/60 truncate">{bug.description}</p>
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-sm text-white/60">{formatDate(bug.created_at)}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default BugStatusTracker;
