import { test, expect } from '@playwright/test';

test.describe('Project Wizard - Critical Fix Verification', () => {
  test('should verify both JSX runtime and JavaScript scoping fixes are working', async ({ page }) => {
    console.log('🧪 Starting comprehensive wizard fix verification test...');
    
    // Navigate to login page
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in with test credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Navigate to Start page
    console.log('🚀 Navigating to Start page...');
    await page.goto('https://royalty.technology/start');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot of Start page
    await page.screenshot({ 
      path: 'test-results/start-page-before-wizard.png', 
      fullPage: true 
    });
    
    // Click "Start Project Wizard" button
    console.log('🎯 Clicking Start Project Wizard button...');
    const wizardButton = page.locator('button:has-text("Start Project Wizard")');
    await expect(wizardButton).toBeVisible();
    await wizardButton.click();
    
    // Wait for navigation to wizard
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Give extra time for React components to mount
    
    // Verify we're on the wizard page
    console.log('📍 Verifying wizard page URL...');
    expect(page.url()).toContain('/project/wizard');
    
    // Take screenshot of wizard page
    await page.screenshot({ 
      path: 'test-results/wizard-page-after-fixes.png', 
      fullPage: true 
    });
    
    // Check for JavaScript errors in console
    console.log('🔍 Checking for JavaScript errors...');
    const jsErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
        console.log('❌ JavaScript Error:', msg.text());
      }
    });
    
    // Wait a bit more to catch any delayed errors
    await page.waitForTimeout(2000);
    
    // Verify no critical JSX runtime errors
    const hasJSXRuntimeError = jsErrors.some(error => 
      error.includes('jsxDEV is not a function') || 
      error.includes('React is not defined')
    );
    
    // Verify no JavaScript scoping errors
    const hasVariableScopingError = jsErrors.some(error => 
      error.includes('projectIdFromPath') || 
      error.includes('ReferenceError')
    );
    
    console.log('🔍 Critical Error Check Results:');
    console.log('  - JSX Runtime Errors:', hasJSXRuntimeError ? '❌ FOUND' : '✅ NONE');
    console.log('  - Variable Scoping Errors:', hasVariableScopingError ? '❌ FOUND' : '✅ NONE');
    console.log('  - Total JS Errors:', jsErrors.length);
    
    // Test for wizard form elements
    console.log('🔍 Testing for wizard form elements...');
    
    // Look for input fields
    const inputFields = await page.locator('input').count();
    console.log(`📝 Found ${inputFields} input fields`);
    
    // Look for wizard steps or navigation
    const wizardSteps = await page.locator('[data-testid*="step"], .wizard-step, .step').count();
    console.log(`📋 Found ${wizardSteps} wizard step elements`);
    
    // Look for form buttons
    const formButtons = await page.locator('button').count();
    console.log(`🔘 Found ${formButtons} buttons`);
    
    // Look for any HeroUI/NextUI components that should now be rendering
    const heroUIComponents = await page.locator('[class*="nextui"], [class*="heroui"], [data-slot]').count();
    console.log(`🎨 Found ${heroUIComponents} HeroUI/NextUI components`);
    
    // Test specific wizard functionality
    console.log('🧪 Testing wizard functionality...');
    
    // Try to interact with the first input field if it exists
    if (inputFields > 0) {
      const firstInput = page.locator('input').first();
      await firstInput.click();
      await firstInput.fill('Test Project Name');
      console.log('✅ Successfully interacted with input field');
    }
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/wizard-interaction-test.png', 
      fullPage: true 
    });
    
    // Assertions
    expect(hasJSXRuntimeError).toBe(false);
    expect(hasVariableScopingError).toBe(false);
    expect(inputFields).toBeGreaterThan(0); // Should have form fields now
    
    console.log('🎉 Test completed successfully!');
    console.log('📊 Summary:');
    console.log(`  - JSX Runtime Fix: ${hasJSXRuntimeError ? '❌ FAILED' : '✅ SUCCESS'}`);
    console.log(`  - Variable Scoping Fix: ${hasVariableScopingError ? '❌ FAILED' : '✅ SUCCESS'}`);
    console.log(`  - Form Elements: ${inputFields > 0 ? '✅ FOUND' : '❌ MISSING'}`);
    console.log(`  - Wizard Components: ${wizardSteps > 0 ? '✅ FOUND' : '❌ MISSING'}`);
  });
});
