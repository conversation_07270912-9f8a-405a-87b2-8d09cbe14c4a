import { test, expect } from '@playwright/test';

/**
 * Human-Like Navigation Testing for Royaltea Platform
 * 
 * This test suite mimics how a real user would navigate the website:
 * - Uses visual cues and text content to find elements
 * - Follows natural user flows and decision-making
 * - Validates the complete user experience end-to-end
 * - Tests against production environment for real-world accuracy
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Human-Like Production Readiness Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Start fresh for each test
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
  });

  test('Complete User Journey: Login → Create Studio → Create Project → Track Tasks', async ({ page }) => {
    console.log('🚀 Starting complete user journey test...');

    // Step 1: Login (like a human would)
    console.log('📝 Step 1: Logging in...');
    
    // Look for login form elements by their visible text/labels
    const emailField = page.locator('input[type="email"], input[placeholder*="email" i], input[name*="email" i]').first();
    const passwordField = page.locator('input[type="password"], input[placeholder*="password" i], input[name*="password" i]').first();
    const loginButton = page.locator('button:has-text("Sign In"), button:has-text("Login"), button:has-text("Log In"), button[type="submit"]').first();

    await emailField.fill(TEST_USER.email);
    await passwordField.fill(TEST_USER.password);
    await loginButton.click();

    // Wait for successful login (look for dashboard indicators)
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/dashboard|start|track/);
    console.log('✅ Login successful');

    // Step 2: Navigate to Start page (project creation)
    console.log('📝 Step 2: Navigating to project creation...');
    
    // Look for Start navigation - could be button, link, or nav item
    const startNavigation = page.locator('a:has-text("Start"), button:has-text("Start"), [href*="start"], nav a:has-text("Start")').first();
    await startNavigation.click();
    await page.waitForLoadState('networkidle');
    console.log('✅ Navigated to Start page');

    // Step 3: Create a studio (integrated into project creation)
    console.log('📝 Step 3: Creating studio through project wizard...');
    
    // Look for project creation button/link
    const createProjectButton = page.locator('button:has-text("Create"), button:has-text("New Project"), a:has-text("Create Project"), button:has-text("Start Project")').first();
    await createProjectButton.click();
    await page.waitForLoadState('networkidle');

    // Should be in project wizard - look for studio creation step
    const studioNameField = page.locator('input[placeholder*="studio" i], input[placeholder*="name" i], input[name*="name" i]').first();
    await studioNameField.fill('Test Studio ' + Date.now());

    // Look for business type selection
    const businessTypeSelector = page.locator('select, [role="combobox"], input[type="radio"]').first();
    if (await businessTypeSelector.isVisible()) {
      await businessTypeSelector.click();
      // Select individual or business option
      const individualOption = page.locator('option:has-text("Individual"), [role="option"]:has-text("Individual"), input[value*="individual"]').first();
      if (await individualOption.isVisible()) {
        await individualOption.click();
      }
    }

    // Continue through wizard steps
    const continueButton = page.locator('button:has-text("Continue"), button:has-text("Next"), button:has-text("Create")').first();
    await continueButton.click();
    await page.waitForLoadState('networkidle');
    console.log('✅ Studio creation initiated');

    // Step 4: Complete project creation
    console.log('📝 Step 4: Completing project creation...');
    
    // Fill project details
    const projectNameField = page.locator('input[placeholder*="project" i], input[placeholder*="name" i]').first();
    await projectNameField.fill('Test Project ' + Date.now());

    const projectDescField = page.locator('textarea, input[placeholder*="description" i]').first();
    if (await projectDescField.isVisible()) {
      await projectDescField.fill('Test project for production readiness validation');
    }

    // Complete project creation
    const createButton = page.locator('button:has-text("Create"), button:has-text("Finish"), button:has-text("Complete")').first();
    await createButton.click();
    await page.waitForLoadState('networkidle');
    console.log('✅ Project creation completed');

    // Step 5: Navigate to Track page
    console.log('📝 Step 5: Navigating to Track page...');
    
    const trackNavigation = page.locator('a:has-text("Track"), button:has-text("Track"), [href*="track"]').first();
    await trackNavigation.click();
    await page.waitForLoadState('networkidle');
    console.log('✅ Navigated to Track page');

    // Step 6: Test kanban board functionality
    console.log('📝 Step 6: Testing kanban board...');
    
    // Look for kanban columns
    const kanbanColumns = page.locator('[class*="kanban"], [class*="column"], [class*="board"]');
    await expect(kanbanColumns).toBeVisible();

    // Try to create a new task
    const addTaskButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create"), [title*="add" i]').first();
    if (await addTaskButton.isVisible()) {
      await addTaskButton.click();
      await page.waitForLoadState('networkidle');

      // Fill task details
      const taskNameField = page.locator('input[placeholder*="task" i], input[placeholder*="name" i]').first();
      if (await taskNameField.isVisible()) {
        await taskNameField.fill('Test Task ' + Date.now());
        
        const saveTaskButton = page.locator('button:has-text("Save"), button:has-text("Create"), button:has-text("Add")').first();
        await saveTaskButton.click();
        await page.waitForLoadState('networkidle');
      }
    }
    console.log('✅ Kanban board functionality tested');

    // Step 7: Navigate to Earn page
    console.log('📝 Step 7: Testing Earn page...');
    
    const earnNavigation = page.locator('a:has-text("Earn"), button:has-text("Earn"), [href*="earn"]').first();
    await earnNavigation.click();
    await page.waitForLoadState('networkidle');

    // Verify gigwork functionality is present
    const gigworkElements = page.locator('[class*="gigwork"], [class*="gig"], text="gigwork"');
    await expect(gigworkElements.first()).toBeVisible();
    console.log('✅ Earn page functionality verified');

    console.log('🎉 Complete user journey test passed!');
  });

  test('Visual UI Quality Assessment', async ({ page }) => {
    console.log('🎨 Starting visual UI quality assessment...');

    // Login first
    const emailField = page.locator('input[type="email"]').first();
    const passwordField = page.locator('input[type="password"]').first();
    const loginButton = page.locator('button[type="submit"], button:has-text("Sign In")').first();

    await emailField.fill(TEST_USER.email);
    await passwordField.fill(TEST_USER.password);
    await loginButton.click();
    await page.waitForLoadState('networkidle');

    // Test each main page for visual quality
    const pages = ['start', 'track', 'earn'];

    for (const pageName of pages) {
      console.log(`🔍 Testing ${pageName} page visual quality...`);

      const navigation = page.locator(`a:has-text("${pageName.charAt(0).toUpperCase() + pageName.slice(1)}")`).first();
      await navigation.click();
      await page.waitForLoadState('networkidle');

      // Take screenshot for visual analysis
      await page.screenshot({
        path: `test-results/${pageName}-page-visual.png`,
        fullPage: true
      });

      // Advanced visual testing - check for contrast issues
      const textElements = await page.locator('*:has-text(/\\w+/)').all();
      let contrastIssues = 0;

      for (const element of textElements.slice(0, 20)) { // Sample first 20 elements
        try {
          const styles = await element.evaluate(el => {
            const computed = window.getComputedStyle(el);
            return {
              color: computed.color,
              backgroundColor: computed.backgroundColor,
              opacity: computed.opacity,
              visibility: computed.visibility
            };
          });

          // Check for potential contrast issues
          if (styles.color === 'rgb(107, 114, 128)' && styles.backgroundColor === 'rgba(0, 0, 0, 0)') {
            contrastIssues++;
          }

          if (styles.opacity === '0' || styles.visibility === 'hidden') {
            console.warn(`⚠️ Hidden element found on ${pageName} page`);
          }
        } catch (e) {
          // Skip elements that can't be evaluated
        }
      }

      if (contrastIssues > 0) {
        console.warn(`⚠️ Found ${contrastIssues} potential contrast issues on ${pageName} page`);
      }

      // Check for functional elements
      const buttons = await page.locator('button, [role="button"]').count();
      const links = await page.locator('a[href]').count();
      const inputs = await page.locator('input, textarea, select').count();

      console.log(`✅ ${pageName} page: ${buttons} buttons, ${links} links, ${inputs} inputs`);

      // Verify no "under construction" or placeholder content
      const placeholderText = page.locator('text=/under construction|coming soon|placeholder|lorem ipsum/i');
      const placeholderCount = await placeholderText.count();
      if (placeholderCount > 0) {
        console.error(`❌ Found ${placeholderCount} placeholder content on ${pageName} page`);
      }
    }

    console.log('🎨 Visual UI quality assessment completed');
  });

  test('Error Handling and Edge Cases', async ({ page }) => {
    console.log('🚨 Testing error handling and edge cases...');

    // Test invalid login
    const emailField = page.locator('input[type="email"]').first();
    const passwordField = page.locator('input[type="password"]').first();
    const loginButton = page.locator('button[type="submit"], button:has-text("Sign In")').first();

    await emailField.fill('<EMAIL>');
    await passwordField.fill('wrongpassword');
    await loginButton.click();
    await page.waitForLoadState('networkidle');

    // Should show error message
    const errorMessage = page.locator('[class*="error"], [class*="alert"], text="Invalid"');
    const hasError = await errorMessage.count() > 0;
    console.log(hasError ? '✅ Error handling working' : '⚠️ No error message shown for invalid login');

    console.log('🚨 Error handling test completed');
  });

  test('Intelligent Content-Based Navigation', async ({ page }) => {
    console.log('🧠 Starting intelligent content-based navigation...');

    // Login first
    const emailField = page.locator('input[type="email"]').first();
    const passwordField = page.locator('input[type="password"]').first();
    const loginButton = page.locator('button[type="submit"], button:has-text("Sign In")').first();

    await emailField.fill(TEST_USER.email);
    await passwordField.fill(TEST_USER.password);
    await loginButton.click();
    await page.waitForLoadState('networkidle');

    // Intelligent navigation: Read page content and make decisions
    const pageContent = await page.textContent('body');
    console.log('📖 Reading page content to understand available options...');

    // Look for project creation options intelligently
    if (pageContent.includes('Create') || pageContent.includes('New Project') || pageContent.includes('Start')) {
      console.log('🎯 Found project creation options, attempting to create project...');

      // Find the most likely project creation element
      const createOptions = [
        'button:has-text("Create Project")',
        'button:has-text("New Project")',
        'a:has-text("Create Project")',
        'button:has-text("Create")',
        'button:has-text("Start")',
        '[href*="project"][href*="create"]',
        '[href*="project"][href*="new"]'
      ];

      let projectCreated = false;
      for (const selector of createOptions) {
        const element = page.locator(selector).first();
        if (await element.isVisible()) {
          console.log(`🎯 Clicking: ${selector}`);
          await element.click();
          await page.waitForLoadState('networkidle');
          projectCreated = true;
          break;
        }
      }

      if (projectCreated) {
        // Intelligently fill out the form based on what we find
        const currentContent = await page.textContent('body');

        if (currentContent.includes('studio') || currentContent.includes('Studio')) {
          console.log('🏢 Studio creation detected, filling studio information...');

          // Find name field intelligently
          const nameFields = [
            'input[placeholder*="name" i]',
            'input[name*="name"]',
            'input[id*="name"]',
            'input[type="text"]'
          ];

          for (const selector of nameFields) {
            const field = page.locator(selector).first();
            if (await field.isVisible()) {
              await field.fill('AI Test Studio ' + Date.now());
              console.log(`✅ Filled name field: ${selector}`);
              break;
            }
          }

          // Look for continue/next buttons
          const continueButtons = [
            'button:has-text("Continue")',
            'button:has-text("Next")',
            'button:has-text("Create")',
            'button[type="submit"]'
          ];

          for (const selector of continueButtons) {
            const button = page.locator(selector).first();
            if (await button.isVisible()) {
              await button.click();
              await page.waitForLoadState('networkidle');
              console.log(`✅ Clicked continue: ${selector}`);
              break;
            }
          }
        }

        console.log('✅ Project creation flow completed');
      }
    }

    // Test Track page functionality intelligently
    console.log('🎯 Looking for Track functionality...');
    const trackElements = [
      'a:has-text("Track")',
      'button:has-text("Track")',
      '[href*="track"]',
      'nav a:contains("Track")'
    ];

    for (const selector of trackElements) {
      const element = page.locator(selector).first();
      if (await element.isVisible()) {
        await element.click();
        await page.waitForLoadState('networkidle');
        console.log('✅ Navigated to Track page');

        // Analyze Track page content
        const trackContent = await page.textContent('body');
        if (trackContent.includes('kanban') || trackContent.includes('task') || trackContent.includes('project')) {
          console.log('✅ Track page has expected project management content');
        }
        break;
      }
    }

    console.log('🧠 Intelligent navigation test completed');
  });
});
