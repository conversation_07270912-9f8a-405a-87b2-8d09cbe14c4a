<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Template Variable Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .variable-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .variable-item {
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .replaced { background-color: #d4edda; }
        .unreplaced { background-color: #f8d7da; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Comprehensive Template Variable Test</h1>
        <p>This test verifies that all template variables are properly mapped and replaced in the comprehensive contributor agreement template.</p>
        
        <button onclick="runTest()">🚀 Run Comprehensive Variable Test</button>
        <button onclick="testTemplateProcessing()">🔧 Test Template Processing</button>
        <button onclick="showVariableMapping()">📋 Show Variable Mapping</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function runTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Running comprehensive variable test...</h3></div>';
            
            try {
                // Load the comprehensive template
                console.log('📥 Loading comprehensive template...');
                const response = await fetch('/templates/v2/comprehensive_contributor_agreement_template.md');
                
                if (!response.ok) {
                    throw new Error(`Template fetch failed: ${response.status}`);
                }
                
                const templateText = await response.text();
                console.log('✅ Template loaded:', templateText.length, 'characters');
                
                // Extract all variables from template
                const variableMatches = templateText.match(/\{\{[A-Z_]+\}\}/g) || [];
                const conditionalMatches = templateText.match(/\{\{#IF\s+[A-Z_]+\}\}/g) || [];
                const uniqueVariables = [...new Set(variableMatches)];
                const uniqueConditionals = [...new Set(conditionalMatches.map(m => m.replace(/\{\{#IF\s+|\}\}/g, '')))];
                
                // Create mock variable mapping (same as in ReviewAgreement.jsx)
                const mockVariables = createMockVariableMapping();
                
                // Test variable replacement
                let processedTemplate = templateText;
                const replacedVariables = [];
                const unreplacedVariables = [];
                
                // Replace variables
                Object.entries(mockVariables).forEach(([key, value]) => {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    const matches = templateText.match(regex);
                    if (matches) {
                        processedTemplate = processedTemplate.replace(regex, String(value));
                        replacedVariables.push(`{{${key}}}`);
                    }
                });
                
                // Check for unreplaced variables
                const remainingVariables = processedTemplate.match(/\{\{[A-Z_]+\}\}/g) || [];
                unreplacedVariables.push(...remainingVariables);
                
                // Display results
                displayResults({
                    templateLength: templateText.length,
                    totalVariables: uniqueVariables.length,
                    totalConditionals: uniqueConditionals.length,
                    replacedCount: replacedVariables.length,
                    unreplacedCount: unreplacedVariables.length,
                    replacedVariables,
                    unreplacedVariables,
                    uniqueVariables,
                    uniqueConditionals,
                    mockVariables
                });
                
            } catch (error) {
                console.error('❌ Test failed:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        function createMockVariableMapping() {
            const currentDate = new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            return {
                // Company Information
                COMPANY_NAME: 'Test Project Owner',
                COMPANY_LEGAL_NAME: 'Test Project Owner LLC',
                COMPANY_STATE: 'Florida',
                COMPANY_ADDRESS: '123 Business St, City, FL 12345',
                COMPANY_COUNTY: 'Miami-Dade County',
                COMPANY_SIGNER_NAME: 'Test Project Owner',
                COMPANY_SIGNER_TITLE: 'Project Owner',
                COMPANY_BILLING_EMAIL: '<EMAIL>',
                
                // Contributor Information
                CONTRIBUTOR_NAME: 'Test Contributor',
                CONTRIBUTOR_EMAIL: '<EMAIL>',
                CONTRIBUTOR_COMPANY_NAME: 'Contributor LLC',
                CONTRIBUTOR_SIGNER_NAME: 'Test Contributor',
                CONTRIBUTOR_SIGNER_TITLE: 'Contributor',
                
                // Project Information
                PROJECT_NAME: 'Test Project',
                PROJECT_DESCRIPTION: 'A test collaborative project',
                PROJECT_TYPE: 'Software',
                PROJECT_CORE_FEATURES: 'Core features to be defined',
                PROJECT_TECHNICAL_REQUIREMENTS: 'Technical requirements to be defined',
                PROJECT_DETAILED_SPECS: 'Detailed specifications to be defined',
                PROJECT_ROADMAP: 'Development roadmap to be defined',
                PROJECT_MILESTONES: 'Milestones to be defined',
                
                // Date Information
                EFFECTIVE_DATE: currentDate,
                CURRENT_DATE: currentDate,
                
                // Revenue Information
                PLATFORM_FEE: '5%',
                REVENUE_MODEL: 'weighted',
                REVENUE_SHARE_PERCENTAGE: '70',
                PAYOUT_THRESHOLD: '1000',
                MAX_INDIVIDUAL_PAYMENT: '50000',
                
                // Contribution Weights
                TASKS_WEIGHT: '30',
                HOURS_WEIGHT: '30',
                DIFFICULTY_WEIGHT: '40',
                
                // Legal and Restrictive Covenant Information
                RESTRICTIVE_COVENANT_YEARS: '2',
                RESTRICTED_BUSINESS_SCOPE: 'software development and distribution',
                RESTRICTED_TERRITORY: 'Florida',
                BUYOUT_AMOUNT: '25000',
                
                // Feature Flags for Optional Sections
                INCLUDE_RESTRICTIVE_COVENANTS: true,
                INCLUDE_SEQUEL_RIGHTS: true,
                INCLUDE_BUYOUT_PROVISIONS: true,
                JURY_TRIAL_WAIVER: true,
                
                // Project type flags for conditional logic
                PROJECT_TYPE_GAME: false,
                PROJECT_TYPE_SOFTWARE: true,
                PROJECT_TYPE_MUSIC: false,
                PROJECT_TYPE_FILM: false,
                PROJECT_TYPE_ART: false
            };
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            
            const successRate = ((results.replacedCount / results.totalVariables) * 100).toFixed(1);
            const isSuccess = results.unreplacedCount === 0;
            
            resultsDiv.innerHTML = `
                <div class="test-section ${isSuccess ? 'success' : 'error'}">
                    <h3>${isSuccess ? '✅' : '⚠️'} Variable Replacement Test Results</h3>
                    
                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-number">${results.templateLength.toLocaleString()}</div>
                            <div>Template Characters</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${results.totalVariables}</div>
                            <div>Total Variables</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${results.replacedCount}</div>
                            <div>Variables Replaced</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${results.unreplacedCount}</div>
                            <div>Variables Remaining</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${successRate}%</div>
                            <div>Success Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${results.totalConditionals}</div>
                            <div>Conditional Blocks</div>
                        </div>
                    </div>
                    
                    ${results.unreplacedCount > 0 ? `
                        <h4>❌ Unreplaced Variables (${results.unreplacedCount}):</h4>
                        <div class="variable-list">
                            ${results.unreplacedVariables.map(v => `<div class="variable-item unreplaced">${v}</div>`).join('')}
                        </div>
                    ` : '<h4>✅ All variables successfully replaced!</h4>'}
                    
                    <details>
                        <summary><strong>📋 All Template Variables (${results.totalVariables})</strong></summary>
                        <div class="variable-list">
                            ${results.uniqueVariables.map(v => `
                                <div class="variable-item ${results.replacedVariables.includes(v) ? 'replaced' : 'unreplaced'}">
                                    ${v}
                                </div>
                            `).join('')}
                        </div>
                    </details>
                    
                    <details>
                        <summary><strong>🔧 Conditional Blocks (${results.totalConditionals})</strong></summary>
                        <div class="variable-list">
                            ${results.uniqueConditionals.map(c => `<div class="variable-item">${c}</div>`).join('')}
                        </div>
                    </details>
                    
                    <details>
                        <summary><strong>🗺️ Variable Mapping (${Object.keys(results.mockVariables).length})</strong></summary>
                        <pre>${JSON.stringify(results.mockVariables, null, 2)}</pre>
                    </details>
                </div>
            `;
        }
        
        async function testTemplateProcessing() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Testing template processing...</h3></div>';
            
            try {
                const response = await fetch('/templates/v2/comprehensive_contributor_agreement_template.md');
                const templateText = await response.text();
                const mockVariables = createMockVariableMapping();
                
                // Simulate the exact processing from ReviewAgreement.jsx
                let processedTemplate = templateText;
                
                // Replace variables
                Object.entries(mockVariables).forEach(([key, value]) => {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    const stringValue = String(value);
                    processedTemplate = processedTemplate.replace(regex, stringValue);
                });
                
                // Process conditional blocks
                const conditionalRegex = /\{\{#IF\s+([^}]+)\}\}([\s\S]*?)\{\{\/IF\}\}/g;
                processedTemplate = processedTemplate.replace(conditionalRegex, (match, condition, content) => {
                    const conditionValue = mockVariables[condition.trim()];
                    return (conditionValue === true || conditionValue === 'true') ? content : '';
                });
                
                const remainingVariables = processedTemplate.match(/\{\{[A-Z_]+\}\}/g) || [];
                
                resultsDiv.innerHTML = `
                    <div class="test-section ${remainingVariables.length === 0 ? 'success' : 'error'}">
                        <h3>🔧 Template Processing Results</h3>
                        <p><strong>Original Length:</strong> ${templateText.length.toLocaleString()} characters</p>
                        <p><strong>Processed Length:</strong> ${processedTemplate.length.toLocaleString()} characters</p>
                        <p><strong>Variables Replaced:</strong> ${remainingVariables.length === 0 ? 'Yes' : 'No'}</p>
                        <p><strong>Remaining Variables:</strong> ${remainingVariables.length}</p>
                        
                        ${remainingVariables.length > 0 ? `
                            <h4>❌ Remaining Variables:</h4>
                            <div class="variable-list">
                                ${remainingVariables.map(v => `<div class="variable-item unreplaced">${v}</div>`).join('')}
                            </div>
                        ` : '<h4>✅ All variables successfully processed!</h4>'}
                        
                        <details>
                            <summary><strong>📄 Processed Agreement Preview (First 2000 characters)</strong></summary>
                            <pre>${processedTemplate.substring(0, 2000)}...</pre>
                        </details>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Processing Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function showVariableMapping() {
            const mockVariables = createMockVariableMapping();
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = `
                <div class="test-section info">
                    <h3>🗺️ Current Variable Mapping</h3>
                    <p>This shows all variables that are currently mapped in the ReviewAgreement component:</p>
                    <pre>${JSON.stringify(mockVariables, null, 2)}</pre>
                </div>
            `;
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(runTest, 1000);
        });
    </script>
</body>
</html>
