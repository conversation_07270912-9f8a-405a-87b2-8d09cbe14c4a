import { createClient } from '@supabase/supabase-js';

// Use service role key to bypass RLS policies for data insertion
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, serviceRoleKey);

async function addTestPaymentData() {
  console.log('🚀 Adding test payment data...');

  // First, check if the payment_transactions table exists
  console.log('🔍 Checking payment_transactions table...');
  const { data: tableCheck, error: tableError } = await supabase
    .from('payment_transactions')
    .select('count', { count: 'exact', head: true });

  if (tableError) {
    console.error('❌ payment_transactions table check failed:', JSON.stringify(tableError, null, 2));
    return;
  }

  console.log('✅ payment_transactions table exists, current count:', tableCheck);

  // First, get the test user ID from auth.users
  const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

  if (authError) {
    console.error('❌ Could not list users:', authError);
    return;
  }

  const testUser = authUsers.users.find(user => user.email === '<EMAIL>');
  if (!testUser) {
    console.error('❌ Could not find test user <NAME_EMAIL>');
    return;
  }

  console.log('✅ Found test user:', testUser.id);

  // Create a second test user for transactions
  let secondUser = authUsers.users.find(user => user.email === '<EMAIL>');

  let secondUserId = secondUser?.id;

  if (!secondUser) {
    console.log('📝 Creating second test user for transactions...');
    // Create a second user if it doesn't exist
    const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      email_confirm: true
    });

    if (createError) {
      console.error('❌ Could not create second user:', createError);
      return;
    }

    secondUserId = newUser.user.id;
    console.log('✅ Created second test user:', secondUserId);
  } else {
    secondUserId = secondUser.id;
    console.log('✅ Found second test user:', secondUserId);
  }

  // Add test payment transactions
  const testPayments = [
    {
      from_user_id: secondUserId,
      to_user_id: testUser.id,
      amount: 1250.00,
      currency: 'USD',
      status: 'completed',
      reference_type: 'project_payment',
      description: 'Frontend Development - Project Alpha',
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days ago
    },
    {
      from_user_id: secondUserId,
      to_user_id: testUser.id,
      amount: 750.00,
      currency: 'USD',
      status: 'completed',
      reference_type: 'royalty_payment',
      description: 'Royalty Distribution - Q4 2024',
      created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString() // 14 days ago
    },
    {
      from_user_id: secondUserId,
      to_user_id: testUser.id,
      amount: 500.00,
      currency: 'USD',
      status: 'pending',
      reference_type: 'milestone_payment',
      description: 'Milestone 3 Completion - Project Beta',
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
    },
    {
      from_user_id: secondUserId,
      to_user_id: testUser.id,
      amount: 2000.00,
      currency: 'USD',
      status: 'processing',
      reference_type: 'gigwork_payment',
      description: 'UI/UX Design Services - Mobile App',
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
    },
    {
      from_user_id: secondUserId,
      to_user_id: testUser.id,
      amount: 300.00,
      currency: 'USD',
      status: 'completed',
      reference_type: 'bonus_payment',
      description: 'Performance Bonus - Exceptional Work',
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
    }
  ];

  console.log('📝 Inserting payment transactions one by one...');
  const paymentData = [];

  for (let i = 0; i < testPayments.length; i++) {
    const payment = testPayments[i];
    console.log(`   Inserting payment ${i + 1}/${testPayments.length}: ${payment.description}`);

    const { data: singlePayment, error: singleError } = await supabase
      .from('payment_transactions')
      .insert([payment])
      .select();

    if (singleError) {
      console.error(`❌ Error inserting payment ${i + 1}:`, JSON.stringify(singleError, null, 2));
      console.error(`❌ Payment data:`, JSON.stringify(payment, null, 2));
      continue;
    }

    if (singlePayment && singlePayment.length > 0) {
      paymentData.push(singlePayment[0]);
      console.log(`   ✅ Successfully inserted payment ${i + 1}`);
    }
  }

  console.log('✅ Successfully added payment transactions:', paymentData.length);

  // Add test escrow accounts
  const { data: projects, error: projectError } = await supabase
    .from('projects')
    .select('id')
    .limit(2);

  if (projectError || !projects.length) {
    console.log('⚠️ No projects found for escrow accounts');
  } else {
    const testEscrowAccounts = [
      {
        project_id: projects[0].id,
        escrow_name: 'Project Alpha Escrow',
        total_amount: 5000.00,
        current_balance: 2500.00,
        depositor_user_id: secondUserId,
        beneficiary_user_id: testUser.id,
        release_conditions: {
          milestones: ['Design Complete', 'Development Complete', 'Testing Complete'],
          approval_required: true
        },
        status: 'active'
      }
    ];

    if (projects.length > 1) {
      testEscrowAccounts.push({
        project_id: projects[1].id,
        escrow_name: 'Project Beta Escrow',
        total_amount: 3000.00,
        current_balance: 3000.00,
        depositor_user_id: secondUserId,
        beneficiary_user_id: testUser.id,
        release_conditions: {
          milestones: ['Phase 1', 'Phase 2'],
          approval_required: false
        },
        status: 'active'
      });
    }

    const { data: escrowData, error: escrowError } = await supabase
      .from('escrow_accounts')
      .insert(testEscrowAccounts)
      .select();

    if (escrowError) {
      console.error('❌ Error inserting escrow accounts:', escrowError);
    } else {
      console.log('✅ Successfully added escrow accounts:', escrowData.length);
    }
  }

  console.log('🎉 Test payment data setup complete!');
  console.log('📊 Summary:');
  console.log(`   - Payment Transactions: ${paymentData.length}`);
  console.log(`   - Completed Payments: ${paymentData.filter(p => p.status === 'completed').length}`);
  console.log(`   - Pending Payments: ${paymentData.filter(p => p.status === 'pending').length}`);
  console.log(`   - Total Completed Amount: $${paymentData.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.amount, 0)}`);
  console.log(`   - Total Pending Amount: $${paymentData.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0)}`);
}

addTestPaymentData().catch(console.error);
