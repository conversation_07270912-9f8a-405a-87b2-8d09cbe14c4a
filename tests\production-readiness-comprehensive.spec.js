/**
 * COMPREHENSIVE PRODUCTION READINESS ASSESSMENT
 * 
 * This test suite evaluates the complete Royaltea platform for production launch readiness
 * by testing all critical user flows in the Start-Track-Earn journey:
 * 
 * 1. Authentication & User Setup
 * 2. Studio Management (Create/Join)
 * 3. Project Management (Create/Join)
 * 4. Task Management & Kanban
 * 5. Gigwork System (Create/Apply/Complete)
 * 6. Payment Integration (Teller)
 * 7. UI/UX Quality Assessment
 * 
 * Test Credentials: <EMAIL> / TestPassword123!
 */

import { test, expect } from '@playwright/test';

// Test configuration
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

// Production readiness report data
let productionReport = {
  timestamp: new Date().toISOString(),
  overallStatus: 'UNKNOWN',
  testResults: {},
  criticalIssues: [],
  warnings: [],
  recommendations: [],
  userFlowStatus: {
    authentication: 'PENDING',
    studioManagement: 'PENDING',
    projectManagement: 'PENDING',
    taskManagement: 'PENDING',
    gigworkSystem: 'PENDING',
    paymentIntegration: 'PENDING',
    uiuxQuality: 'PENDING'
  }
};

// Helper function to update report status
function updateReportStatus(flow, status, details = null, issues = []) {
  productionReport.userFlowStatus[flow] = status;
  productionReport.testResults[flow] = {
    status,
    details,
    issues,
    timestamp: new Date().toISOString()
  };
  
  if (issues.length > 0) {
    productionReport.criticalIssues.push(...issues.filter(i => i.severity === 'CRITICAL'));
    productionReport.warnings.push(...issues.filter(i => i.severity === 'WARNING'));
  }
}

// Helper function to take screenshot with context
async function takeContextualScreenshot(page, context, step) {
  const filename = `production-test-${context}-${step}-${Date.now()}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  return filename;
}

test.describe('Production Readiness Assessment - Comprehensive User Journey', () => {
  let page;
  let context;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    page = await context.newPage();
    
    // Enable console logging for debugging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🚨 Console Error: ${msg.text()}`);
        productionReport.criticalIssues.push({
          type: 'CONSOLE_ERROR',
          severity: 'CRITICAL',
          message: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });

    // Track network failures
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log(`🚨 Network Error: ${response.status()} - ${response.url()}`);
        productionReport.warnings.push({
          type: 'NETWORK_ERROR',
          severity: 'WARNING',
          message: `${response.status()} error on ${response.url()}`,
          timestamp: new Date().toISOString()
        });
      }
    });
  });

  test.afterAll(async () => {
    // Calculate overall status
    const statuses = Object.values(productionReport.userFlowStatus);
    const passedCount = statuses.filter(s => s === 'PASSED').length;
    const failedCount = statuses.filter(s => s === 'FAILED').length;
    const criticalIssueCount = productionReport.criticalIssues.length;
    
    if (criticalIssueCount > 0 || failedCount > 0) {
      productionReport.overallStatus = 'NOT_READY';
    } else if (passedCount === statuses.length) {
      productionReport.overallStatus = 'READY';
    } else {
      productionReport.overallStatus = 'NEEDS_REVIEW';
    }

    // Generate comprehensive report
    console.log('\n' + '='.repeat(80));
    console.log('🏆 ROYALTEA PRODUCTION READINESS ASSESSMENT REPORT');
    console.log('='.repeat(80));
    console.log(`📅 Assessment Date: ${productionReport.timestamp}`);
    console.log(`🎯 Overall Status: ${productionReport.overallStatus}`);
    console.log(`🔍 Critical Issues: ${productionReport.criticalIssues.length}`);
    console.log(`⚠️  Warnings: ${productionReport.warnings.length}`);
    
    console.log('\n📊 USER FLOW STATUS:');
    Object.entries(productionReport.userFlowStatus).forEach(([flow, status]) => {
      const emoji = status === 'PASSED' ? '✅' : status === 'FAILED' ? '❌' : '⏳';
      console.log(`  ${emoji} ${flow}: ${status}`);
    });

    if (productionReport.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:');
      productionReport.criticalIssues.forEach((issue, i) => {
        console.log(`  ${i + 1}. [${issue.type}] ${issue.message}`);
      });
    }

    if (productionReport.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      productionReport.warnings.slice(0, 10).forEach((warning, i) => {
        console.log(`  ${i + 1}. [${warning.type}] ${warning.message}`);
      });
      if (productionReport.warnings.length > 10) {
        console.log(`  ... and ${productionReport.warnings.length - 10} more warnings`);
      }
    }

    console.log('\n📋 PRODUCTION READINESS RECOMMENDATIONS:');
    if (productionReport.overallStatus === 'READY') {
      console.log('  ✅ Platform is ready for production launch');
      console.log('  ✅ All critical user flows are functional');
      console.log('  ✅ No blocking issues identified');
    } else if (productionReport.overallStatus === 'NOT_READY') {
      console.log('  ❌ Platform is NOT ready for production launch');
      console.log('  ❌ Critical issues must be resolved before launch');
      console.log('  ❌ User flows have blocking problems');
    } else {
      console.log('  ⚠️  Platform needs review before production launch');
      console.log('  ⚠️  Some user flows need attention');
      console.log('  ⚠️  Address warnings for optimal user experience');
    }

    console.log('='.repeat(80));

    await context.close();
  });

  // Test 1: Authentication & User Setup
  test('1. Authentication & User Setup Flow', async () => {
    console.log('\n🔐 Testing Authentication & User Setup...');
    
    try {
      // Navigate to production site
      await page.goto(PRODUCTION_URL);
      await page.waitForLoadState('networkidle');
      
      const screenshot1 = await takeContextualScreenshot(page, 'auth', 'initial-load');
      console.log(`📸 Initial load screenshot: ${screenshot1}`);

      // Check if we're redirected to login (unauthenticated users should see login)
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);

      // Look for login form elements
      const emailInput = page.locator('input[type="email"], input[name="email"], input[placeholder*="email" i]');
      const passwordInput = page.locator('input[type="password"], input[name="password"], input[placeholder*="password" i]');
      const loginButton = page.locator('button:has-text("Sign In"), button:has-text("Login"), button[type="submit"]');

      // Wait for login form to be visible
      await expect(emailInput).toBeVisible({ timeout: 10000 });
      await expect(passwordInput).toBeVisible({ timeout: 5000 });
      await expect(loginButton).toBeVisible({ timeout: 5000 });

      console.log('✅ Login form elements found');

      // Perform authentication
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      
      const screenshot2 = await takeContextualScreenshot(page, 'auth', 'credentials-filled');
      console.log(`📸 Credentials filled screenshot: ${screenshot2}`);

      await loginButton.click();
      
      // Wait for authentication to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Allow for any redirects

      const screenshot3 = await takeContextualScreenshot(page, 'auth', 'post-login');
      console.log(`📸 Post-login screenshot: ${screenshot3}`);

      // Verify successful authentication by checking for dashboard elements
      const dashboardIndicators = [
        page.locator('text=Dashboard'),
        page.locator('text=Start'),
        page.locator('text=Track'),
        page.locator('text=Earn'),
        page.locator('[data-testid="user-profile"]'),
        page.locator('button:has-text("Profile")'),
        page.locator('.user-avatar'),
        page.locator('text=Welcome')
      ];

      let authSuccess = false;
      for (const indicator of dashboardIndicators) {
        try {
          await indicator.waitFor({ timeout: 5000 });
          authSuccess = true;
          console.log('✅ Dashboard element found - authentication successful');
          break;
        } catch (e) {
          // Continue checking other indicators
        }
      }

      if (!authSuccess) {
        // Check if we're still on login page or got an error
        const errorMessages = await page.locator('.error, .alert-error, [role="alert"]').allTextContents();
        if (errorMessages.length > 0) {
          throw new Error(`Authentication failed with errors: ${errorMessages.join(', ')}`);
        } else {
          throw new Error('Authentication status unclear - no dashboard indicators found');
        }
      }

      updateReportStatus('authentication', 'PASSED', {
        loginFormFound: true,
        credentialsAccepted: true,
        dashboardAccess: true,
        screenshots: [screenshot1, screenshot2, screenshot3]
      });

      console.log('✅ Authentication & User Setup: PASSED');

    } catch (error) {
      console.log(`❌ Authentication & User Setup: FAILED - ${error.message}`);
      
      const errorScreenshot = await takeContextualScreenshot(page, 'auth', 'error');
      
      updateReportStatus('authentication', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'AUTHENTICATION_FAILURE',
        severity: 'CRITICAL',
        message: `Authentication failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
      
      throw error; // Re-throw to fail the test
    }
  });

  // Test 2: Studio Management Flow
  test('2. Studio Management Flow (Create/Join)', async () => {
    console.log('\n🏢 Testing Studio Management Flow...');

    try {
      // Navigate to Start page (studio management)
      await page.goto(`${PRODUCTION_URL}/#/start`);
      await page.waitForLoadState('networkidle');

      const screenshot1 = await takeContextualScreenshot(page, 'studio', 'start-page');
      console.log(`📸 Start page screenshot: ${screenshot1}`);

      // Look for studio creation elements
      const createStudioButton = page.locator('button:has-text("Create Studio"), button:has-text("New Studio"), button:has-text("Create")').first();
      const studioList = page.locator('[data-testid="studio-list"], .studio-list, .studios-container');

      // Check if studios are displayed or creation option is available
      let studioManagementFound = false;

      try {
        await createStudioButton.waitFor({ timeout: 5000 });
        studioManagementFound = true;
        console.log('✅ Studio creation button found');
      } catch (e) {
        console.log('⚠️ Studio creation button not immediately visible');
      }

      try {
        await studioList.waitFor({ timeout: 5000 });
        studioManagementFound = true;
        console.log('✅ Studio list found');
      } catch (e) {
        console.log('⚠️ Studio list not immediately visible');
      }

      if (!studioManagementFound) {
        // Look for any studio-related content
        const studioContent = await page.locator('text=studio, text=Studio, text=alliance, text=Alliance').count();
        if (studioContent > 0) {
          studioManagementFound = true;
          console.log('✅ Studio-related content found');
        }
      }

      // Test studio creation if button is available
      let studioCreated = false;
      if (await createStudioButton.isVisible()) {
        try {
          await createStudioButton.click();
          await page.waitForTimeout(2000);

          const screenshot2 = await takeContextualScreenshot(page, 'studio', 'creation-modal');
          console.log(`📸 Studio creation modal screenshot: ${screenshot2}`);

          // Look for studio creation form
          const studioNameInput = page.locator('input[name="name"], input[placeholder*="name" i], input[placeholder*="studio" i]').first();
          const studioDescInput = page.locator('textarea[name="description"], textarea[placeholder*="description" i]').first();
          const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")').first();

          if (await studioNameInput.isVisible()) {
            await studioNameInput.fill(`Test Studio ${Date.now()}`);
            console.log('✅ Studio name filled');
          }

          if (await studioDescInput.isVisible()) {
            await studioDescInput.fill('Test studio for production readiness assessment');
            console.log('✅ Studio description filled');
          }

          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(3000);
            studioCreated = true;
            console.log('✅ Studio creation attempted');
          }

        } catch (error) {
          console.log(`⚠️ Studio creation flow error: ${error.message}`);
        }
      }

      const screenshot3 = await takeContextualScreenshot(page, 'studio', 'final-state');
      console.log(`📸 Final studio state screenshot: ${screenshot3}`);

      updateReportStatus('studioManagement', 'PASSED', {
        studioManagementFound,
        studioCreated,
        screenshots: [screenshot1, screenshot3]
      });

      console.log('✅ Studio Management Flow: PASSED');

    } catch (error) {
      console.log(`❌ Studio Management Flow: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'studio', 'error');

      updateReportStatus('studioManagement', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'STUDIO_MANAGEMENT_FAILURE',
        severity: 'CRITICAL',
        message: `Studio management failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

  // Test 3: Project Management Flow
  test('3. Project Management Flow (Create/Join)', async () => {
    console.log('\n📋 Testing Project Management Flow...');

    try {
      // Navigate to Track page (project management)
      await page.goto(`${PRODUCTION_URL}/#/track`);
      await page.waitForLoadState('networkidle');

      const screenshot1 = await takeContextualScreenshot(page, 'project', 'track-page');
      console.log(`📸 Track page screenshot: ${screenshot1}`);

      // Look for project management elements
      const createProjectButton = page.locator('button:has-text("Create Project"), button:has-text("New Project"), button:has-text("Add Project")').first();
      const projectList = page.locator('[data-testid="project-list"], .project-list, .projects-container');
      const projectTabs = page.locator('.project-tabs, [role="tablist"]');

      let projectManagementFound = false;

      // Check for project creation capability
      try {
        await createProjectButton.waitFor({ timeout: 5000 });
        projectManagementFound = true;
        console.log('✅ Project creation button found');
      } catch (e) {
        console.log('⚠️ Project creation button not immediately visible');
      }

      // Check for existing projects
      try {
        await projectList.waitFor({ timeout: 5000 });
        projectManagementFound = true;
        console.log('✅ Project list found');
      } catch (e) {
        console.log('⚠️ Project list not immediately visible');
      }

      // Check for project tabs
      try {
        await projectTabs.waitFor({ timeout: 5000 });
        projectManagementFound = true;
        console.log('✅ Project tabs found');
      } catch (e) {
        console.log('⚠️ Project tabs not immediately visible');
      }

      if (!projectManagementFound) {
        // Look for any project-related content
        const projectContent = await page.locator('text=project, text=Project, text=venture, text=Venture').count();
        if (projectContent > 0) {
          projectManagementFound = true;
          console.log('✅ Project-related content found');
        }
      }

      // Test project creation if available
      let projectCreated = false;
      if (await createProjectButton.isVisible()) {
        try {
          await createProjectButton.click();
          await page.waitForTimeout(2000);

          const screenshot2 = await takeContextualScreenshot(page, 'project', 'creation-modal');
          console.log(`📸 Project creation modal screenshot: ${screenshot2}`);

          // Look for project creation form
          const projectNameInput = page.locator('input[name="name"], input[placeholder*="name" i], input[placeholder*="project" i]').first();
          const projectDescInput = page.locator('textarea[name="description"], textarea[placeholder*="description" i]').first();
          const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")').first();

          if (await projectNameInput.isVisible()) {
            await projectNameInput.fill(`Test Project ${Date.now()}`);
            console.log('✅ Project name filled');
          }

          if (await projectDescInput.isVisible()) {
            await projectDescInput.fill('Test project for production readiness assessment');
            console.log('✅ Project description filled');
          }

          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(3000);
            projectCreated = true;
            console.log('✅ Project creation attempted');
          }

        } catch (error) {
          console.log(`⚠️ Project creation flow error: ${error.message}`);
        }
      }

      const screenshot3 = await takeContextualScreenshot(page, 'project', 'final-state');
      console.log(`📸 Final project state screenshot: ${screenshot3}`);

      updateReportStatus('projectManagement', 'PASSED', {
        projectManagementFound,
        projectCreated,
        screenshots: [screenshot1, screenshot3]
      });

      console.log('✅ Project Management Flow: PASSED');

    } catch (error) {
      console.log(`❌ Project Management Flow: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'project', 'error');

      updateReportStatus('projectManagement', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'PROJECT_MANAGEMENT_FAILURE',
        severity: 'CRITICAL',
        message: `Project management failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

  // Test 4: Task Management & Kanban Flow
  test('4. Task Management & Kanban Flow', async () => {
    console.log('\n📝 Testing Task Management & Kanban Flow...');

    try {
      // Stay on Track page or navigate to kanban view
      await page.goto(`${PRODUCTION_URL}/#/track`);
      await page.waitForLoadState('networkidle');

      const screenshot1 = await takeContextualScreenshot(page, 'task', 'kanban-view');
      console.log(`📸 Kanban view screenshot: ${screenshot1}`);

      // Look for kanban board elements
      const kanbanBoard = page.locator('.kanban-board, [data-testid="kanban-board"], .board-container');
      const kanbanColumns = page.locator('.kanban-column, .column, [data-testid="kanban-column"]');
      const createTaskButton = page.locator('button:has-text("Create Task"), button:has-text("New Task"), button:has-text("Add Task")').first();
      const taskCards = page.locator('.task-card, .card, [data-testid="task-card"]');

      let kanbanFound = false;
      let columnsFound = false;
      let tasksFound = false;

      // Check for kanban board
      try {
        await kanbanBoard.waitFor({ timeout: 5000 });
        kanbanFound = true;
        console.log('✅ Kanban board found');
      } catch (e) {
        console.log('⚠️ Kanban board not immediately visible');
      }

      // Check for kanban columns (Todo, In Progress, Done, etc.)
      try {
        await kanbanColumns.first().waitFor({ timeout: 5000 });
        const columnCount = await kanbanColumns.count();
        if (columnCount >= 3) {
          columnsFound = true;
          console.log(`✅ Kanban columns found (${columnCount} columns)`);
        }
      } catch (e) {
        console.log('⚠️ Kanban columns not found');
      }

      // Check for existing tasks
      try {
        const taskCount = await taskCards.count();
        if (taskCount > 0) {
          tasksFound = true;
          console.log(`✅ Task cards found (${taskCount} tasks)`);
        }
      } catch (e) {
        console.log('⚠️ No task cards found');
      }

      // Test task creation if button is available
      let taskCreated = false;
      if (await createTaskButton.isVisible()) {
        try {
          await createTaskButton.click();
          await page.waitForTimeout(2000);

          const screenshot2 = await takeContextualScreenshot(page, 'task', 'creation-modal');
          console.log(`📸 Task creation modal screenshot: ${screenshot2}`);

          // Look for task creation form
          const taskTitleInput = page.locator('input[name="title"], input[placeholder*="title" i], input[placeholder*="task" i]').first();
          const taskDescInput = page.locator('textarea[name="description"], textarea[placeholder*="description" i]').first();
          const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")').first();

          if (await taskTitleInput.isVisible()) {
            await taskTitleInput.fill(`Test Task ${Date.now()}`);
            console.log('✅ Task title filled');
          }

          if (await taskDescInput.isVisible()) {
            await taskDescInput.fill('Test task for production readiness assessment');
            console.log('✅ Task description filled');
          }

          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(3000);
            taskCreated = true;
            console.log('✅ Task creation attempted');
          }

        } catch (error) {
          console.log(`⚠️ Task creation flow error: ${error.message}`);
        }
      }

      // Test drag and drop functionality if tasks exist
      let dragDropTested = false;
      if (tasksFound || taskCreated) {
        try {
          const firstTask = taskCards.first();
          const targetColumn = kanbanColumns.nth(1); // Move to second column

          if (await firstTask.isVisible() && await targetColumn.isVisible()) {
            await firstTask.dragTo(targetColumn);
            await page.waitForTimeout(2000);
            dragDropTested = true;
            console.log('✅ Drag and drop functionality tested');
          }
        } catch (error) {
          console.log(`⚠️ Drag and drop test error: ${error.message}`);
        }
      }

      const screenshot3 = await takeContextualScreenshot(page, 'task', 'final-state');
      console.log(`📸 Final task state screenshot: ${screenshot3}`);

      const taskManagementWorking = kanbanFound || columnsFound || tasksFound;

      updateReportStatus('taskManagement', taskManagementWorking ? 'PASSED' : 'FAILED', {
        kanbanFound,
        columnsFound,
        tasksFound,
        taskCreated,
        dragDropTested,
        screenshots: [screenshot1, screenshot3]
      }, taskManagementWorking ? [] : [{
        type: 'TASK_MANAGEMENT_MISSING',
        severity: 'CRITICAL',
        message: 'Task management and kanban functionality not found',
        timestamp: new Date().toISOString()
      }]);

      console.log(`${taskManagementWorking ? '✅' : '❌'} Task Management & Kanban Flow: ${taskManagementWorking ? 'PASSED' : 'FAILED'}`);

    } catch (error) {
      console.log(`❌ Task Management & Kanban Flow: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'task', 'error');

      updateReportStatus('taskManagement', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'TASK_MANAGEMENT_FAILURE',
        severity: 'CRITICAL',
        message: `Task management failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

  // Test 5: Gigwork System Flow
  test('5. Gigwork System Flow (Create/Apply/Complete)', async () => {
    console.log('\n💼 Testing Gigwork System Flow...');

    try {
      // Navigate to Earn page (gigwork system)
      await page.goto(`${PRODUCTION_URL}/#/earn`);
      await page.waitForLoadState('networkidle');

      const screenshot1 = await takeContextualScreenshot(page, 'gigwork', 'earn-page');
      console.log(`📸 Earn page screenshot: ${screenshot1}`);

      // Look for gigwork elements
      const createGigButton = page.locator('button:has-text("Create Gig"), button:has-text("New Gig"), button:has-text("Post Gig")').first();
      const gigList = page.locator('[data-testid="gig-list"], .gig-list, .gigs-container, .mission-board');
      const gigCards = page.locator('.gig-card, .mission-card, [data-testid="gig-card"]');
      const applyButton = page.locator('button:has-text("Apply"), button:has-text("Apply for Gig")').first();

      let gigworkFound = false;
      let gigsAvailable = false;

      // Check for gig creation capability
      try {
        await createGigButton.waitFor({ timeout: 5000 });
        gigworkFound = true;
        console.log('✅ Gig creation button found');
      } catch (e) {
        console.log('⚠️ Gig creation button not immediately visible');
      }

      // Check for gig listings
      try {
        await gigList.waitFor({ timeout: 5000 });
        gigworkFound = true;
        console.log('✅ Gig list found');
      } catch (e) {
        console.log('⚠️ Gig list not immediately visible');
      }

      // Check for existing gigs
      try {
        const gigCount = await gigCards.count();
        if (gigCount > 0) {
          gigsAvailable = true;
          console.log(`✅ Gig cards found (${gigCount} gigs)`);
        }
      } catch (e) {
        console.log('⚠️ No gig cards found');
      }

      if (!gigworkFound) {
        // Look for any gigwork-related content
        const gigworkContent = await page.locator('text=gig, text=Gig, text=mission, text=Mission, text=freelance, text=Freelance').count();
        if (gigworkContent > 0) {
          gigworkFound = true;
          console.log('✅ Gigwork-related content found');
        }
      }

      // Test gig creation if button is available
      let gigCreated = false;
      if (await createGigButton.isVisible()) {
        try {
          await createGigButton.click();
          await page.waitForTimeout(2000);

          const screenshot2 = await takeContextualScreenshot(page, 'gigwork', 'creation-modal');
          console.log(`📸 Gig creation modal screenshot: ${screenshot2}`);

          // Look for gig creation form
          const gigTitleInput = page.locator('input[name="title"], input[placeholder*="title" i], input[placeholder*="gig" i]').first();
          const gigDescInput = page.locator('textarea[name="description"], textarea[placeholder*="description" i]').first();
          const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Post")').first();

          if (await gigTitleInput.isVisible()) {
            await gigTitleInput.fill(`Test Gig ${Date.now()}`);
            console.log('✅ Gig title filled');
          }

          if (await gigDescInput.isVisible()) {
            await gigDescInput.fill('Test gig for production readiness assessment');
            console.log('✅ Gig description filled');
          }

          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(3000);
            gigCreated = true;
            console.log('✅ Gig creation attempted');
          }

        } catch (error) {
          console.log(`⚠️ Gig creation flow error: ${error.message}`);
        }
      }

      const screenshot3 = await takeContextualScreenshot(page, 'gigwork', 'final-state');
      console.log(`📸 Final gigwork state screenshot: ${screenshot3}`);

      updateReportStatus('gigworkSystem', gigworkFound ? 'PASSED' : 'FAILED', {
        gigworkFound,
        gigsAvailable,
        gigCreated,
        screenshots: [screenshot1, screenshot3]
      }, gigworkFound ? [] : [{
        type: 'GIGWORK_SYSTEM_MISSING',
        severity: 'CRITICAL',
        message: 'Gigwork system functionality not found',
        timestamp: new Date().toISOString()
      }]);

      console.log(`${gigworkFound ? '✅' : '❌'} Gigwork System Flow: ${gigworkFound ? 'PASSED' : 'FAILED'}`);

    } catch (error) {
      console.log(`❌ Gigwork System Flow: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'gigwork', 'error');

      updateReportStatus('gigworkSystem', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'GIGWORK_SYSTEM_FAILURE',
        severity: 'CRITICAL',
        message: `Gigwork system failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

  // Test 6: Payment & Teller Integration Flow
  test('6. Payment & Teller Integration Flow', async () => {
    console.log('\n💳 Testing Payment & Teller Integration Flow...');

    try {
      // Check for payment-related pages and components
      const paymentPages = [
        `${PRODUCTION_URL}/#/revenue`,
        `${PRODUCTION_URL}/#/payments`,
        `${PRODUCTION_URL}/#/royalty`,
        `${PRODUCTION_URL}/#/treasury`
      ];

      let paymentSystemFound = false;
      let tellerIntegrationFound = false;
      let paymentScreenshots = [];

      for (const pageUrl of paymentPages) {
        try {
          await page.goto(pageUrl);
          await page.waitForLoadState('networkidle');

          const screenshot = await takeContextualScreenshot(page, 'payment', `page-${pageUrl.split('/').pop()}`);
          paymentScreenshots.push(screenshot);
          console.log(`📸 Payment page screenshot: ${screenshot}`);

          // Look for payment-related elements
          const paymentElements = [
            page.locator('text=Teller, text=payment, text=Payment, text=revenue, text=Revenue'),
            page.locator('button:has-text("Connect Bank"), button:has-text("Link Account")'),
            page.locator('.payment-method, .bank-account, .teller-link'),
            page.locator('[data-testid="payment-dashboard"], [data-testid="teller-component"]')
          ];

          for (const element of paymentElements) {
            try {
              await element.first().waitFor({ timeout: 3000 });
              paymentSystemFound = true;
              console.log('✅ Payment system elements found');
              break;
            } catch (e) {
              // Continue checking
            }
          }

          // Specifically check for Teller integration
          const tellerElements = [
            page.locator('text=Teller'),
            page.locator('.teller-link, .teller-component'),
            page.locator('button:has-text("Connect Bank Account")')
          ];

          for (const element of tellerElements) {
            try {
              await element.first().waitFor({ timeout: 3000 });
              tellerIntegrationFound = true;
              console.log('✅ Teller integration elements found');
              break;
            } catch (e) {
              // Continue checking
            }
          }

          if (paymentSystemFound) break;

        } catch (error) {
          console.log(`⚠️ Error checking payment page ${pageUrl}: ${error.message}`);
        }
      }

      // Test payment connection flow if available
      let paymentConnectionTested = false;
      const connectButton = page.locator('button:has-text("Connect Bank"), button:has-text("Link Account"), button:has-text("Add Payment Method")').first();

      if (await connectButton.isVisible()) {
        try {
          await connectButton.click();
          await page.waitForTimeout(3000);

          const connectionScreenshot = await takeContextualScreenshot(page, 'payment', 'connection-flow');
          paymentScreenshots.push(connectionScreenshot);
          console.log(`📸 Payment connection screenshot: ${connectionScreenshot}`);

          paymentConnectionTested = true;
          console.log('✅ Payment connection flow initiated');
        } catch (error) {
          console.log(`⚠️ Payment connection test error: ${error.message}`);
        }
      }

      updateReportStatus('paymentIntegration', paymentSystemFound ? 'PASSED' : 'FAILED', {
        paymentSystemFound,
        tellerIntegrationFound,
        paymentConnectionTested,
        screenshots: paymentScreenshots
      }, paymentSystemFound ? [] : [{
        type: 'PAYMENT_SYSTEM_MISSING',
        severity: 'CRITICAL',
        message: 'Payment system and Teller integration not found',
        timestamp: new Date().toISOString()
      }]);

      console.log(`${paymentSystemFound ? '✅' : '❌'} Payment & Teller Integration Flow: ${paymentSystemFound ? 'PASSED' : 'FAILED'}`);

    } catch (error) {
      console.log(`❌ Payment & Teller Integration Flow: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'payment', 'error');

      updateReportStatus('paymentIntegration', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'PAYMENT_INTEGRATION_FAILURE',
        severity: 'CRITICAL',
        message: `Payment integration failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

  // Test 7: UI/UX Quality Assessment
  test('7. UI/UX Quality Assessment', async () => {
    console.log('\n🎨 Testing UI/UX Quality...');

    try {
      const uiIssues = [];
      const uiScreenshots = [];

      // Test main pages for UI quality
      const mainPages = [
        { url: `${PRODUCTION_URL}/#/`, name: 'dashboard' },
        { url: `${PRODUCTION_URL}/#/start`, name: 'start' },
        { url: `${PRODUCTION_URL}/#/track`, name: 'track' },
        { url: `${PRODUCTION_URL}/#/earn`, name: 'earn' }
      ];

      let overallUiQuality = true;

      for (const pageInfo of mainPages) {
        try {
          await page.goto(pageInfo.url);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000); // Allow animations to complete

          const screenshot = await takeContextualScreenshot(page, 'ui', pageInfo.name);
          uiScreenshots.push(screenshot);
          console.log(`📸 UI screenshot for ${pageInfo.name}: ${screenshot}`);

          // Check for common UI issues

          // 1. Check for horizontal overflow
          const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
          const viewportWidth = await page.evaluate(() => window.innerWidth);
          if (bodyWidth > viewportWidth + 10) { // 10px tolerance
            uiIssues.push({
              type: 'HORIZONTAL_OVERFLOW',
              severity: 'WARNING',
              page: pageInfo.name,
              message: `Horizontal overflow detected on ${pageInfo.name} page (${bodyWidth}px > ${viewportWidth}px)`,
              timestamp: new Date().toISOString()
            });
          }

          // 2. Check for missing content or placeholder text
          const placeholderText = await page.locator('text=Lorem ipsum, text=placeholder, text=TODO, text=Coming soon, text=Under construction').count();
          if (placeholderText > 0) {
            uiIssues.push({
              type: 'PLACEHOLDER_CONTENT',
              severity: 'WARNING',
              page: pageInfo.name,
              message: `Placeholder content found on ${pageInfo.name} page`,
              timestamp: new Date().toISOString()
            });
          }

          // 3. Check for broken images
          const images = await page.locator('img').all();
          for (const img of images) {
            const naturalWidth = await img.evaluate(el => el.naturalWidth);
            if (naturalWidth === 0) {
              uiIssues.push({
                type: 'BROKEN_IMAGE',
                severity: 'WARNING',
                page: pageInfo.name,
                message: `Broken image detected on ${pageInfo.name} page`,
                timestamp: new Date().toISOString()
              });
            }
          }

          // 4. Check for console errors (already tracked globally)

          // 5. Check for basic responsiveness
          await page.setViewportSize({ width: 768, height: 1024 }); // Tablet
          await page.waitForTimeout(1000);

          const tabletScreenshot = await takeContextualScreenshot(page, 'ui', `${pageInfo.name}-tablet`);
          uiScreenshots.push(tabletScreenshot);

          await page.setViewportSize({ width: 375, height: 667 }); // Mobile
          await page.waitForTimeout(1000);

          const mobileScreenshot = await takeContextualScreenshot(page, 'ui', `${pageInfo.name}-mobile`);
          uiScreenshots.push(mobileScreenshot);

          // Reset to desktop
          await page.setViewportSize({ width: 1920, height: 1080 });
          await page.waitForTimeout(1000);

          console.log(`✅ UI quality check completed for ${pageInfo.name}`);

        } catch (error) {
          console.log(`⚠️ UI quality check error for ${pageInfo.name}: ${error.message}`);
          uiIssues.push({
            type: 'UI_CHECK_ERROR',
            severity: 'WARNING',
            page: pageInfo.name,
            message: `UI check failed for ${pageInfo.name}: ${error.message}`,
            timestamp: new Date().toISOString()
          });
          overallUiQuality = false;
        }
      }

      // Determine UI quality status
      const criticalUiIssues = uiIssues.filter(issue => issue.severity === 'CRITICAL').length;
      const warningUiIssues = uiIssues.filter(issue => issue.severity === 'WARNING').length;

      let uiStatus = 'PASSED';
      if (criticalUiIssues > 0) {
        uiStatus = 'FAILED';
        overallUiQuality = false;
      } else if (warningUiIssues > 5) {
        uiStatus = 'NEEDS_REVIEW';
      }

      updateReportStatus('uiuxQuality', uiStatus, {
        overallUiQuality,
        criticalIssues: criticalUiIssues,
        warnings: warningUiIssues,
        screenshots: uiScreenshots
      }, uiIssues);

      console.log(`${overallUiQuality ? '✅' : '❌'} UI/UX Quality Assessment: ${uiStatus}`);
      console.log(`   Critical Issues: ${criticalUiIssues}, Warnings: ${warningUiIssues}`);

    } catch (error) {
      console.log(`❌ UI/UX Quality Assessment: FAILED - ${error.message}`);

      const errorScreenshot = await takeContextualScreenshot(page, 'ui', 'error');

      updateReportStatus('uiuxQuality', 'FAILED', {
        error: error.message,
        screenshot: errorScreenshot
      }, [{
        type: 'UI_ASSESSMENT_FAILURE',
        severity: 'CRITICAL',
        message: `UI/UX assessment failed: ${error.message}`,
        timestamp: new Date().toISOString()
      }]);
    }
  });

});
