// Production Systems Verification Test Suite
// Tests project pages styling, friend system, vetting system, and learning system functionality
// Verifies real data integration and external platform support

import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Production Systems Verification Tests', () => {
  
  test('1. Project Pages Styling Verification', async ({ page }) => {
    console.log('🎨 Testing project pages styling and layout...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Login first to access project pages
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    const loginButton = page.locator('button:has-text("Log In")');
    
    if (await emailInput.count() > 0) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      await loginButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Look for project navigation or links
    const projectLinks = await page.locator('a[href*="/project/"], a:has-text("Project"), button:has-text("Project")').count();
    
    if (projectLinks > 0) {
      // Click on first project link
      await page.locator('a[href*="/project/"], a:has-text("Project")').first().click();
      await page.waitForTimeout(2000);
      
      // Take screenshot for verification
      await page.screenshot({
        path: `test-results/project-page-styling-${Date.now()}.png`,
        fullPage: true
      });
      
      // Check page content for project styling
      const content = await page.content();
      
      // Verify project page styling elements are present
      const stylingElements = [
        'project-detail-container',
        'project-header', 
        'project-title',
        'project-content',
        'project-tabs',
        'project-actions'
      ];
      
      let stylingScore = 0;
      for (const element of stylingElements) {
        if (content.includes(element)) {
          stylingScore++;
        }
      }
      
      console.log(`📊 Project styling elements found: ${stylingScore}/${stylingElements.length}`);
      
      // Should have proper project page styling
      expect(stylingScore).toBeGreaterThan(2);
      
      console.log('✅ Project pages have proper styling');
    } else {
      console.log('⚠️ No project links found - may need to create projects first');
    }
  });

  test('2. Friend System Real Data Integration', async ({ page }) => {
    console.log('👥 Testing friend system real data integration...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Monitor network requests for friend system API calls
    const friendApiCalls = [];
    page.on('request', request => {
      const url = request.url();
      if (url.includes('friend') || url.includes('allies') || url.includes('social')) {
        friendApiCalls.push(url);
      }
    });
    
    // Login first
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    const loginButton = page.locator('button:has-text("Log In")');
    
    if (await emailInput.count() > 0) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      await loginButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Look for friend/social navigation or buttons
    const socialElements = await page.locator('a:has-text("Friends"), a:has-text("Social"), button:has-text("Add Friend"), a:has-text("Invitations")').count();
    
    if (socialElements > 0) {
      await page.locator('a:has-text("Friends"), a:has-text("Social"), a:has-text("Invitations")').first().click();
      await page.waitForTimeout(2000);
    }
    
    // Check page content for friend system database integration
    const content = await page.content();
    
    // Verify real database tables are referenced
    const friendSystemIndicators = [
      'friend_requests',
      'user_allies', 
      'social_interactions',
      'invitation',
      'ally'
    ];
    
    let friendSystemScore = 0;
    for (const indicator of friendSystemIndicators) {
      if (content.includes(indicator)) {
        friendSystemScore++;
      }
    }
    
    console.log(`📊 Friend system integration indicators: ${friendSystemScore}/${friendSystemIndicators.length}`);
    
    // Check that friend system doesn't use mock data
    const mockFriendPatterns = [
      'mock_friend_',
      'fake_ally_',
      'sample_connection',
      'test_user_friend'
    ];
    
    let mockDataFound = 0;
    for (const pattern of mockFriendPatterns) {
      if (content.includes(pattern)) {
        mockDataFound++;
      }
    }
    
    expect(mockDataFound).toBe(0);
    console.log('✅ Friend system uses real data (no mock patterns found)');
  });

  test('3. Vetting System Real Data Integration', async ({ page }) => {
    console.log('🎓 Testing vetting system real data integration...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Monitor vetting system API calls
    const vettingApiCalls = [];
    page.on('request', request => {
      const url = request.url();
      if (url.includes('vetting') || url.includes('skill') || url.includes('verification')) {
        vettingApiCalls.push(url);
      }
    });
    
    // Login first
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    const loginButton = page.locator('button:has-text("Log In")');
    
    if (await emailInput.count() > 0) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      await loginButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Look for vetting/skills navigation
    const vettingElements = await page.locator('a:has-text("Skills"), a:has-text("Vetting"), a:has-text("Verification"), a:has-text("Learn")').count();
    
    if (vettingElements > 0) {
      await page.locator('a:has-text("Skills"), a:has-text("Vetting"), a:has-text("Verification"), a:has-text("Learn")').first().click();
      await page.waitForTimeout(2000);
    }
    
    // Check for vetting system database integration
    const content = await page.content();
    
    // Verify real vetting database tables
    const vettingIndicators = [
      'vetting_applications',
      'skill_assessments',
      'user_skill_levels',
      'learning_progress',
      'vetting-service'
    ];
    
    let vettingScore = 0;
    for (const indicator of vettingIndicators) {
      if (content.includes(indicator)) {
        vettingScore++;
      }
    }
    
    console.log(`📊 Vetting system integration indicators: ${vettingScore}/${vettingIndicators.length}`);
    
    // Check that vetting system doesn't use mock data
    const mockVettingPatterns = [
      'mock_skill_',
      'fake_assessment_',
      'sample_verification',
      'test_vetting_'
    ];
    
    let mockDataFound = 0;
    for (const pattern of mockVettingPatterns) {
      if (content.includes(pattern)) {
        mockDataFound++;
      }
    }
    
    expect(mockDataFound).toBe(0);
    console.log('✅ Vetting system uses real data (no mock patterns found)');
  });

  test('4. Learning System External Platform Support', async ({ page }) => {
    console.log('📚 Testing learning system external platform support...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Login first
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    const loginButton = page.locator('button:has-text("Log In")');
    
    if (await emailInput.count() > 0) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      await loginButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Look for learning center navigation
    const learningElements = await page.locator('a:has-text("Learn"), a:has-text("Learning"), a:has-text("Education")').count();
    
    if (learningElements > 0) {
      await page.locator('a:has-text("Learn"), a:has-text("Learning"), a:has-text("Education")').first().click();
      await page.waitForTimeout(2000);
    }
    
    // Check page content for external platform integrations
    const content = await page.content();
    
    // Verify support for external learning platforms
    const externalPlatforms = [
      'youtube',
      'linkedin',
      'unreal', 
      'epic',
      'linkedinlearning'
    ];
    
    let platformSupport = 0;
    for (const platform of externalPlatforms) {
      if (content.toLowerCase().includes(platform)) {
        platformSupport++;
      }
    }
    
    console.log(`📊 External platform support found: ${platformSupport}/${externalPlatforms.length}`);
    
    // Should support at least some external platforms
    expect(platformSupport).toBeGreaterThan(0);
    
    // Check for learning system API integration
    const learningApiPatterns = [
      'learning_paths',
      'learning_progress',
      'course_provider',
      'linkedin_learning',
      'linkedInLearningService'
    ];
    
    let apiIntegration = 0;
    for (const pattern of learningApiPatterns) {
      if (content.includes(pattern)) {
        apiIntegration++;
      }
    }
    
    console.log(`📊 Learning API integration indicators: ${apiIntegration}/${learningApiPatterns.length}`);
    expect(apiIntegration).toBeGreaterThan(0);
    
    console.log('✅ Learning system supports external platforms');
  });

  test('5. Learning-Vetting System Integration', async ({ page }) => {
    console.log('🔗 Testing learning-vetting system integration...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check page content for learning-vetting integration
    const content = await page.content();
    
    // Verify integration between learning and vetting systems
    const integrationPatterns = [
      'skill_verification',
      'learning_path',
      'assessment_results',
      'level_advancement',
      'vetting-service',
      'SkillVerificationDashboard',
      'LearningHub'
    ];
    
    let integrationFound = 0;
    for (const pattern of integrationPatterns) {
      if (content.includes(pattern)) {
        integrationFound++;
      }
    }
    
    console.log(`📊 Learning-vetting integration indicators: ${integrationFound}/${integrationPatterns.length}`);
    
    // Should have integration between learning and vetting
    expect(integrationFound).toBeGreaterThan(0);
    
    console.log('✅ Learning and vetting systems are integrated');
  });

  test('6. YouTube Video Support Verification', async ({ page }) => {
    console.log('📹 Testing YouTube video support...');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for YouTube integration in page source
    const content = await page.content();
    
    // Look for YouTube-related code patterns
    const youtubePatterns = [
      'youtube',
      'youtu.be',
      'youtube.com',
      'video_id',
      'embed',
      'YouTube'
    ];
    
    let youtubeSupport = 0;
    for (const pattern of youtubePatterns) {
      if (content.toLowerCase().includes(pattern.toLowerCase())) {
        youtubeSupport++;
      }
    }
    
    console.log(`📊 YouTube integration indicators: ${youtubeSupport}/${youtubePatterns.length}`);
    
    // Should have some YouTube integration
    expect(youtubeSupport).toBeGreaterThan(0);
    
    console.log('✅ YouTube video support is implemented');
  });

});
