import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Chip,
  Divider
} from '@heroui/react';
import { Bug, Send, AlertTriangle, Info, Zap } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const BugReportForm = () => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    severity: '',
    category: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    browserInfo: navigator.userAgent,
    url: window.location.href
  });

  const severityOptions = [
    { key: 'critical', label: 'Critical', color: 'danger', icon: Alert<PERSON>riangle },
    { key: 'high', label: 'High', color: 'warning', icon: Zap },
    { key: 'medium', label: 'Medium', color: 'primary', icon: Info },
    { key: 'low', label: 'Low', color: 'default', icon: Info }
  ];

  const categoryOptions = [
    { key: 'ui', label: 'User Interface' },
    { key: 'functionality', label: 'Functionality' },
    { key: 'performance', label: 'Performance' },
    { key: 'security', label: 'Security' },
    { key: 'data', label: 'Data Issues' },
    { key: 'integration', label: 'Integration' },
    { key: 'other', label: 'Other' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description || !formData.severity) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('bug_reports')
        .insert([{
          title: formData.title,
          description: formData.description,
          severity: formData.severity,
          category: formData.category,
          steps_to_reproduce: formData.stepsToReproduce,
          expected_behavior: formData.expectedBehavior,
          actual_behavior: formData.actualBehavior,
          browser_info: formData.browserInfo,
          url: formData.url,
          reporter_id: user?.id,
          status: 'open',
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;

      toast.success('Bug report submitted successfully!');
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        severity: '',
        category: '',
        stepsToReproduce: '',
        expectedBehavior: '',
        actualBehavior: '',
        browserInfo: navigator.userAgent,
        url: window.location.href
      });

    } catch (error) {
      console.error('Error submitting bug report:', error);
      toast.error('Failed to submit bug report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 max-w-4xl mx-auto"
    >
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-danger/20 flex items-center justify-center">
              <Bug size={24} className="text-danger" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Report a Bug</h2>
              <p className="text-white/70">Help us improve Royaltea by reporting issues you encounter</p>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <Input
              label="Bug Title"
              placeholder="Brief description of the issue"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              isRequired
              variant="bordered"
              classNames={{
                input: "text-white",
                label: "text-white/70"
              }}
            />

            {/* Severity and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Severity"
                placeholder="Select severity level"
                value={formData.severity}
                onChange={(e) => handleInputChange('severity', e.target.value)}
                isRequired
                variant="bordered"
                classNames={{
                  label: "text-white/70",
                  value: "text-white"
                }}
              >
                {severityOptions.map((option) => (
                  <SelectItem key={option.key} value={option.key}>
                    <div className="flex items-center gap-2">
                      <option.icon size={16} />
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </Select>

              <Select
                label="Category"
                placeholder="Select bug category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                variant="bordered"
                classNames={{
                  label: "text-white/70",
                  value: "text-white"
                }}
              >
                {categoryOptions.map((option) => (
                  <SelectItem key={option.key} value={option.key}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            {/* Description */}
            <Textarea
              label="Bug Description"
              placeholder="Detailed description of the bug"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              isRequired
              variant="bordered"
              minRows={4}
              classNames={{
                input: "text-white",
                label: "text-white/70"
              }}
            />

            {/* Steps to Reproduce */}
            <Textarea
              label="Steps to Reproduce"
              placeholder="1. Go to... 2. Click on... 3. See error..."
              value={formData.stepsToReproduce}
              onChange={(e) => handleInputChange('stepsToReproduce', e.target.value)}
              variant="bordered"
              minRows={3}
              classNames={{
                input: "text-white",
                label: "text-white/70"
              }}
            />

            {/* Expected vs Actual Behavior */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Textarea
                label="Expected Behavior"
                placeholder="What should happen?"
                value={formData.expectedBehavior}
                onChange={(e) => handleInputChange('expectedBehavior', e.target.value)}
                variant="bordered"
                minRows={3}
                classNames={{
                  input: "text-white",
                  label: "text-white/70"
                }}
              />

              <Textarea
                label="Actual Behavior"
                placeholder="What actually happens?"
                value={formData.actualBehavior}
                onChange={(e) => handleInputChange('actualBehavior', e.target.value)}
                variant="bordered"
                minRows={3}
                classNames={{
                  input: "text-white",
                  label: "text-white/70"
                }}
              />
            </div>

            <Divider className="bg-white/20" />

            {/* System Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">System Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Current URL"
                  value={formData.url}
                  onChange={(e) => handleInputChange('url', e.target.value)}
                  variant="bordered"
                  classNames={{
                    input: "text-white text-sm",
                    label: "text-white/70"
                  }}
                />
                <Input
                  label="Browser Information"
                  value={formData.browserInfo}
                  onChange={(e) => handleInputChange('browserInfo', e.target.value)}
                  variant="bordered"
                  classNames={{
                    input: "text-white text-sm",
                    label: "text-white/70"
                  }}
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                color="danger"
                size="lg"
                startContent={<Send size={20} />}
                isLoading={isSubmitting}
                className="min-w-[150px]"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Bug Report'}
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default BugReportForm;
