/**
 * KANBAN BOARD DEBUG TEST
 * 
 * This test specifically focuses on debugging the kanban board functionality
 * on the Track page to understand why it's not working properly.
 */

import { test, expect } from '@playwright/test';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Kanban Board Debug', () => {
  let page;
  let context;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    page = await context.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('[KanbanBoard]')) {
        console.log(`🔍 Console ${msg.type()}: ${msg.text()}`);
      }
    });
  });

  test.afterAll(async () => {
    await context.close();
  });

  test('Debug Kanban Board on Track Page', async () => {
    console.log('\n🔍 Starting Kanban Board Debug...');
    
    // Step 1: Login
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
    
    if (await emailInput.isVisible() && await passwordInput.isVisible()) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      
      const submitButton = page.locator('button[type="submit"]').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
      }
    }
    
    // Step 2: Navigate to Track page
    console.log('📍 Navigating to Track page...');
    await page.goto(`${PRODUCTION_URL}/#/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give time for React components to load
    
    // Take screenshot of Track page
    await page.screenshot({ path: 'test-results/kanban-debug-track-page.png', fullPage: true });
    
    // Step 3: Look for kanban board elements
    console.log('🔍 Looking for kanban board elements...');
    
    // Check for task board container
    const taskBoard = page.locator('[data-testid="task-board"]');
    const taskBoardVisible = await taskBoard.isVisible();
    console.log(`📋 Task board container visible: ${taskBoardVisible}`);
    
    // Check for kanban columns
    const kanbanColumns = page.locator('.kanban-column, [class*="kanban"], [class*="column"]');
    const columnCount = await kanbanColumns.count();
    console.log(`📊 Kanban columns found: ${columnCount}`);
    
    // Look for specific column titles
    const columnTitles = ['To Do', 'In Progress', 'Review', 'Done', 'todo', 'in_progress', 'review', 'done'];
    for (const title of columnTitles) {
      const columnExists = await page.locator(`text=${title}`).isVisible();
      if (columnExists) {
        console.log(`✅ Found column: ${title}`);
      }
    }
    
    // Check for tasks
    const tasks = page.locator('.kanban-task, [class*="task"], .task-card');
    const taskCount = await tasks.count();
    console.log(`📝 Tasks found: ${taskCount}`);
    
    // Check for loading states
    const loadingElements = page.locator('text=Loading, text=loading, .loading, .spinner');
    const loadingCount = await loadingElements.count();
    console.log(`⏳ Loading elements: ${loadingCount}`);
    
    // Check for error messages
    const errorElements = page.locator('text=Error, text=error, .error, .alert-danger');
    const errorCount = await errorElements.count();
    console.log(`❌ Error elements: ${errorCount}`);
    
    if (errorCount > 0) {
      for (let i = 0; i < errorCount; i++) {
        const errorText = await errorElements.nth(i).textContent();
        console.log(`🚨 Error ${i + 1}: ${errorText}`);
      }
    }
    
    // Step 4: Check for project selection
    console.log('🏗️ Checking project selection...');
    const projectSelector = page.locator('select, [role="combobox"], .select');
    const projectSelectorCount = await projectSelector.count();
    console.log(`🎯 Project selectors found: ${projectSelectorCount}`);
    
    if (projectSelectorCount > 0) {
      const projectSelectorVisible = await projectSelector.first().isVisible();
      console.log(`🎯 Project selector visible: ${projectSelectorVisible}`);
    }
    
    // Check for "My First Project" or similar
    const projectText = await page.locator('text=Project, text=project').count();
    console.log(`🏗️ Project-related text elements: ${projectText}`);
    
    // Step 5: Look for task creation buttons
    console.log('➕ Looking for task creation elements...');
    const createButtons = page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New"), [aria-label*="create"], [aria-label*="add"]');
    const createButtonCount = await createButtons.count();
    console.log(`➕ Create/Add buttons found: ${createButtonCount}`);
    
    // Step 6: Check page structure
    console.log('🏗️ Analyzing page structure...');
    const mainContent = await page.locator('main, .main, [role="main"]').count();
    console.log(`📄 Main content areas: ${mainContent}`);
    
    const cards = await page.locator('.card, [class*="card"]').count();
    console.log(`🃏 Card elements: ${cards}`);
    
    const grids = await page.locator('.grid, [class*="grid"]').count();
    console.log(`📐 Grid layouts: ${grids}`);
    
    // Step 7: Check for specific kanban board component
    console.log('🎯 Looking for KanbanBoard component specifically...');
    
    // Wait a bit more for async loading
    await page.waitForTimeout(3000);
    
    // Look for kanban-specific classes and elements
    const kanbanBoard = page.locator('[class*="kanban"], [data-testid*="kanban"]');
    const kanbanBoardCount = await kanbanBoard.count();
    console.log(`🎯 Kanban board elements: ${kanbanBoardCount}`);
    
    // Check for drag and drop elements
    const draggableElements = page.locator('[draggable="true"], [class*="draggable"]');
    const draggableCount = await draggableElements.count();
    console.log(`🖱️ Draggable elements: ${draggableCount}`);
    
    // Step 8: Final screenshot and summary
    await page.screenshot({ path: 'test-results/kanban-debug-final.png', fullPage: true });
    
    console.log('\n📊 KANBAN DEBUG SUMMARY:');
    console.log(`- Task board container: ${taskBoardVisible ? 'FOUND' : 'NOT FOUND'}`);
    console.log(`- Kanban columns: ${columnCount}`);
    console.log(`- Tasks: ${taskCount}`);
    console.log(`- Loading elements: ${loadingCount}`);
    console.log(`- Error elements: ${errorCount}`);
    console.log(`- Project selectors: ${projectSelectorCount}`);
    console.log(`- Create buttons: ${createButtonCount}`);
    console.log(`- Kanban board elements: ${kanbanBoardCount}`);
    console.log(`- Draggable elements: ${draggableCount}`);
    
    // Determine status
    if (taskBoardVisible && kanbanBoardCount > 0) {
      console.log('✅ KANBAN BOARD: FOUND AND RENDERED');
    } else if (taskBoardVisible) {
      console.log('⚠️ KANBAN BOARD: CONTAINER FOUND BUT BOARD NOT RENDERED');
    } else {
      console.log('❌ KANBAN BOARD: NOT FOUND');
    }
  });

  test('Check Database Connection for Tasks', async () => {
    console.log('\n🗄️ Checking database connection for tasks...');
    
    // Navigate to track page
    await page.goto(`${PRODUCTION_URL}/#/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check network requests
    const responses = [];
    page.on('response', response => {
      if (response.url().includes('supabase') || response.url().includes('tasks')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });
    
    // Wait for potential API calls
    await page.waitForTimeout(5000);
    
    console.log('🌐 Supabase/Tasks API calls:');
    responses.forEach((response, index) => {
      console.log(`${index + 1}. ${response.status} ${response.statusText} - ${response.url}`);
    });
    
    if (responses.length === 0) {
      console.log('⚠️ No Supabase/Tasks API calls detected');
    }
  });

});
