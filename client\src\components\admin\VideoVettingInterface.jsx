import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Tabs, Tab, Chip, Badge,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  Textarea, Select, SelectItem, Input, Checkbox, Divider,
  Table, TableHeader, TableColumn, TableBody, TableRow, TableCell,
  useDisclosure, Spinner
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Admin Video Vetting Interface
 * 
 * Allows administrators to review submitted videos, mark them for vetting purposes,
 * approve/reject submissions, and manage the learning content quality.
 */
const VideoVettingInterface = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [submissions, setSubmissions] = useState([]);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [reviewForm, setReviewForm] = useState({
    contentQualityScore: 5,
    educationalValueScore: 5,
    productionQualityScore: 5,
    accuracyScore: 5,
    reviewComments: '',
    recommendedAction: 'approve',
    revisionSuggestions: '',
    overallRating: 'good',
    wouldRecommend: true,
    markForVetting: false,
    vettingSkills: [],
    vettingLevel: 'beginner'
  });

  // Load video submissions
  useEffect(() => {
    loadSubmissions();
  }, [activeTab]);

  const loadSubmissions = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('video_submissions')
        .select(`
          *,
          submitter:auth.users!submitted_by(email, user_metadata),
          reviewer:auth.users!reviewed_by(email, user_metadata)
        `)
        .order('created_at', { ascending: false });

      // Filter by status based on active tab
      if (activeTab !== 'all') {
        query = query.eq('status', activeTab);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      setSubmissions(data || []);
      
    } catch (error) {
      console.error('Error loading submissions:', error);
      toast.error('Failed to load video submissions');
    } finally {
      setLoading(false);
    }
  };

  // Handle submission review
  const handleReviewSubmission = async () => {
    if (!selectedSubmission) return;

    try {
      setLoading(true);

      // Create review record
      const reviewData = {
        submission_id: selectedSubmission.id,
        reviewer_id: currentUser.id,
        content_quality_score: reviewForm.contentQualityScore,
        educational_value_score: reviewForm.educationalValueScore,
        production_quality_score: reviewForm.productionQualityScore,
        accuracy_score: reviewForm.accuracyScore,
        review_comments: reviewForm.reviewComments,
        recommended_action: reviewForm.recommendedAction,
        revision_suggestions: reviewForm.revisionSuggestions,
        overall_rating: reviewForm.overallRating,
        would_recommend: reviewForm.wouldRecommend
      };

      const { error: reviewError } = await supabase
        .from('video_submission_reviews')
        .insert([reviewData]);

      if (reviewError) throw reviewError;

      // Update submission status
      const updateData = {
        status: reviewForm.recommendedAction === 'approve' ? 'approved' : 
                reviewForm.recommendedAction === 'reject' ? 'rejected' : 'needs_revision',
        reviewed_by: currentUser.id,
        reviewed_at: new Date().toISOString(),
        review_notes: reviewForm.reviewComments,
        quality_rating: reviewForm.overallRating
      };

      if (reviewForm.recommendedAction === 'approve') {
        updateData.approved_by = currentUser.id;
        updateData.approved_at = new Date().toISOString();
      } else if (reviewForm.recommendedAction === 'reject') {
        updateData.rejection_reason = reviewForm.revisionSuggestions;
      }

      const { error: updateError } = await supabase
        .from('video_submissions')
        .update(updateData)
        .eq('id', selectedSubmission.id);

      if (updateError) throw updateError;

      // If approved and marked for vetting, add to course catalog with vetting flag
      if (reviewForm.recommendedAction === 'approve' && reviewForm.markForVetting) {
        const catalogData = {
          external_id: selectedSubmission.video_id,
          provider: 'youtube',
          title: selectedSubmission.title,
          description: selectedSubmission.description,
          duration_minutes: selectedSubmission.duration_minutes,
          instructor_name: selectedSubmission.channel_name,
          thumbnail_url: `https://img.youtube.com/vi/${selectedSubmission.video_id}/mqdefault.jpg`,
          course_url: selectedSubmission.video_url,
          skills: reviewForm.vettingSkills,
          categories: selectedSubmission.categories,
          difficulty_level: reviewForm.vettingLevel,
          is_active: true,
          is_featured: false,
          // Custom field to mark for vetting
          metadata: {
            marked_for_vetting: true,
            vetting_skills: reviewForm.vettingSkills,
            vetting_level: reviewForm.vettingLevel,
            reviewed_by: currentUser.id,
            review_date: new Date().toISOString()
          }
        };

        const { error: catalogError } = await supabase
          .from('course_catalog')
          .upsert([catalogData], { onConflict: 'external_id,provider' });

        if (catalogError) {
          console.error('Error adding to catalog:', catalogError);
          toast.error('Video approved but failed to add to catalog');
        } else {
          toast.success('Video approved and added to vetting system!');
        }
      } else {
        toast.success(`Video ${reviewForm.recommendedAction}d successfully!`);
      }

      // Reset form and close modal
      setReviewForm({
        contentQualityScore: 5,
        educationalValueScore: 5,
        productionQualityScore: 5,
        accuracyScore: 5,
        reviewComments: '',
        recommendedAction: 'approve',
        revisionSuggestions: '',
        overallRating: 'good',
        wouldRecommend: true,
        markForVetting: false,
        vettingSkills: [],
        vettingLevel: 'beginner'
      });
      
      onClose();
      loadSubmissions(); // Refresh the list

    } catch (error) {
      console.error('Error reviewing submission:', error);
      toast.error('Failed to review submission');
    } finally {
      setLoading(false);
    }
  };

  // Open review modal
  const openReviewModal = (submission) => {
    setSelectedSubmission(submission);
    setReviewForm({
      contentQualityScore: 5,
      educationalValueScore: 5,
      productionQualityScore: 5,
      accuracyScore: 5,
      reviewComments: '',
      recommendedAction: 'approve',
      revisionSuggestions: '',
      overallRating: 'good',
      wouldRecommend: true,
      markForVetting: false,
      vettingSkills: submission.skills || [],
      vettingLevel: submission.difficulty_level || 'beginner'
    });
    onOpen();
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'under_review': return 'primary';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'needs_revision': return 'secondary';
      default: return 'default';
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const availableSkills = [
    'JavaScript', 'Python', 'React', 'Node.js', 'HTML', 'CSS', 'TypeScript',
    'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin',
    'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis',
    'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'DevOps',
    'Machine Learning', 'Data Science', 'AI', 'Deep Learning',
    'UI Design', 'UX Design', 'Figma', 'Adobe XD', 'Photoshop'
  ];

  if (loading && submissions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2">Loading submissions...</span>
      </div>
    );
  }

  return (
    <div className="video-vetting-interface space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Video Vetting Interface</h2>
          <p className="text-default-600">Review and manage submitted educational videos</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge content={submissions.filter(s => s.status === 'pending').length} color="warning">
            <Button color="warning" variant="flat" size="sm">
              Pending Reviews
            </Button>
          </Badge>
        </div>
      </div>

      {/* Status Tabs */}
      <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab}>
        <Tab key="pending" title="Pending">
          <div className="mt-4">
            {submissions.filter(s => s.status === 'pending').length === 0 ? (
              <div className="text-center py-8 text-default-600">
                <i className="bi bi-check-circle text-4xl mb-2 block"></i>
                <p>No pending submissions to review</p>
              </div>
            ) : (
              <div className="grid gap-4">
                {submissions.filter(s => s.status === 'pending').map((submission) => (
                  <Card key={submission.id} className="hover:shadow-lg transition-shadow">
                    <CardBody className="p-4">
                      <div className="flex gap-4">
                        <img
                          src={`https://img.youtube.com/vi/${submission.video_id}/mqdefault.jpg`}
                          alt={submission.title}
                          className="w-32 h-20 object-cover rounded-lg flex-shrink-0"
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold line-clamp-2">{submission.title}</h3>
                            <Chip color={getStatusColor(submission.status)} size="sm" variant="flat">
                              {submission.status.replace('_', ' ')}
                            </Chip>
                          </div>
                          
                          <p className="text-sm text-default-600 line-clamp-2 mb-2">
                            {submission.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-xs text-default-500 mb-3">
                            <span>By: {submission.submitter?.email}</span>
                            <span>Submitted: {formatDate(submission.created_at)}</span>
                            <span>Duration: {submission.duration_minutes || 'Unknown'} min</span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {submission.skills?.slice(0, 3).map((skill) => (
                                <Chip key={skill} size="sm" variant="bordered">
                                  {skill}
                                </Chip>
                              ))}
                              {submission.skills?.length > 3 && (
                                <Chip size="sm" variant="bordered">
                                  +{submission.skills.length - 3} more
                                </Chip>
                              )}
                            </div>
                            
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                color="primary"
                                variant="flat"
                                as="a"
                                href={submission.video_url}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <i className="bi bi-play-circle mr-1"></i>
                                Watch
                              </Button>
                              <Button
                                size="sm"
                                color="success"
                                onClick={() => openReviewModal(submission)}
                              >
                                Review
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </Tab>
        
        <Tab key="approved" title="Approved">
          <div className="mt-4">
            {/* Similar structure for approved submissions */}
            <div className="text-center py-8 text-default-600">
              <i className="bi bi-check-circle text-4xl mb-2 block text-success"></i>
              <p>Approved submissions will appear here</p>
            </div>
          </div>
        </Tab>
        
        <Tab key="rejected" title="Rejected">
          <div className="mt-4">
            {/* Similar structure for rejected submissions */}
            <div className="text-center py-8 text-default-600">
              <i className="bi bi-x-circle text-4xl mb-2 block text-danger"></i>
              <p>Rejected submissions will appear here</p>
            </div>
          </div>
        </Tab>
        
        <Tab key="all" title="All">
          <div className="mt-4">
            <div className="text-sm text-default-600 mb-4">
              Total submissions: {submissions.length}
            </div>
            {/* Table view for all submissions */}
          </div>
        </Tab>
      </Tabs>

      {/* Review Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="4xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Review Video Submission</h3>
          </ModalHeader>
          
          <ModalBody>
            {selectedSubmission && (
              <div className="space-y-6">
                {/* Video Preview */}
                <div className="bg-default-100 p-4 rounded-lg">
                  <div className="flex gap-4">
                    <img
                      src={`https://img.youtube.com/vi/${selectedSubmission.video_id}/mqdefault.jpg`}
                      alt={selectedSubmission.title}
                      className="w-40 h-24 object-cover rounded-lg"
                    />
                    <div>
                      <h4 className="font-semibold mb-1">{selectedSubmission.title}</h4>
                      <p className="text-sm text-default-600 mb-2">{selectedSubmission.description}</p>
                      <Button
                        size="sm"
                        color="primary"
                        as="a"
                        href={selectedSubmission.video_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <i className="bi bi-play-circle mr-1"></i>
                        Watch Video
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Quality Scores */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Content Quality (1-5)</label>
                    <Input
                      type="number"
                      min="1"
                      max="5"
                      value={reviewForm.contentQualityScore}
                      onChange={(e) => setReviewForm({
                        ...reviewForm,
                        contentQualityScore: parseInt(e.target.value)
                      })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Educational Value (1-5)</label>
                    <Input
                      type="number"
                      min="1"
                      max="5"
                      value={reviewForm.educationalValueScore}
                      onChange={(e) => setReviewForm({
                        ...reviewForm,
                        educationalValueScore: parseInt(e.target.value)
                      })}
                    />
                  </div>
                </div>

                {/* Review Decision */}
                <Select
                  label="Review Decision"
                  selectedKeys={[reviewForm.recommendedAction]}
                  onSelectionChange={(keys) => setReviewForm({
                    ...reviewForm,
                    recommendedAction: Array.from(keys)[0]
                  })}
                >
                  <SelectItem key="approve" value="approve">Approve</SelectItem>
                  <SelectItem key="reject" value="reject">Reject</SelectItem>
                  <SelectItem key="needs_revision" value="needs_revision">Needs Revision</SelectItem>
                </Select>

                {/* Vetting Options */}
                <div className="space-y-4">
                  <Checkbox
                    isSelected={reviewForm.markForVetting}
                    onValueChange={(checked) => setReviewForm({
                      ...reviewForm,
                      markForVetting: checked
                    })}
                  >
                    Mark for Vetting System (contributes to skill verification)
                  </Checkbox>

                  {reviewForm.markForVetting && (
                    <div className="ml-6 space-y-3 p-3 bg-primary/10 rounded-lg">
                      <Select
                        label="Vetting Skills"
                        placeholder="Select skills this video helps verify"
                        selectionMode="multiple"
                        selectedKeys={reviewForm.vettingSkills}
                        onSelectionChange={(keys) => setReviewForm({
                          ...reviewForm,
                          vettingSkills: Array.from(keys)
                        })}
                      >
                        {availableSkills.map((skill) => (
                          <SelectItem key={skill} value={skill}>
                            {skill}
                          </SelectItem>
                        ))}
                      </Select>

                      <Select
                        label="Vetting Level"
                        selectedKeys={[reviewForm.vettingLevel]}
                        onSelectionChange={(keys) => setReviewForm({
                          ...reviewForm,
                          vettingLevel: Array.from(keys)[0]
                        })}
                      >
                        <SelectItem key="beginner" value="beginner">Beginner</SelectItem>
                        <SelectItem key="intermediate" value="intermediate">Intermediate</SelectItem>
                        <SelectItem key="advanced" value="advanced">Advanced</SelectItem>
                      </Select>
                    </div>
                  )}
                </div>

                {/* Review Comments */}
                <Textarea
                  label="Review Comments"
                  placeholder="Provide feedback about this video submission..."
                  value={reviewForm.reviewComments}
                  onChange={(e) => setReviewForm({
                    ...reviewForm,
                    reviewComments: e.target.value
                  })}
                  minRows={3}
                />
              </div>
            )}
          </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleReviewSubmission}
              isLoading={loading}
            >
              Submit Review
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default VideoVettingInterface;
