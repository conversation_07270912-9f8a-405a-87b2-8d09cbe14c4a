<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Type Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .task-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Task Type Integration Test</h1>
        <p>This test verifies that the agreement generation system properly uses task types from project data instead of hardcoded values.</p>
        
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        let testResults = [];

        async function runAllTests() {
            clearResults();
            testResults = [];
            
            await testTemplateLoading();
            await testTaskTypeProcessing();
            await testMilestoneFormatting();
            await testConditionalLogic();
            await testVariableReplacement();
            
            displaySummary();
        }

        async function testTemplateLoading() {
            const testName = "Template Loading Test";
            try {
                const response = await fetch('/templates/v2/comprehensive_contributor_agreement_template.md');
                const template = await response.text();
                
                if (template.length > 50000) {
                    addResult(testName, 'success', `✅ Template loaded successfully (${template.length} characters)`);
                    
                    // Check for task type table patterns
                    const hasTaskTypeTables = template.includes('| Task Type | Difficulty Level | Points |');
                    if (hasTaskTypeTables) {
                        addResult(testName, 'info', `📋 Found task type tables in template`);
                    } else {
                        addResult(testName, 'warning', `⚠️ No task type tables found in template`);
                    }
                } else {
                    addResult(testName, 'error', `❌ Template too short: ${template.length} characters`);
                }
            } catch (error) {
                addResult(testName, 'error', `❌ Template loading failed: ${error.message}`);
            }
        }

        async function testTaskTypeProcessing() {
            const testName = "Task Type Processing Test";
            
            // Mock project data with different task types
            const mockProjects = [
                {
                    name: "Game Project",
                    type: "game",
                    contribution_tracking: {
                        task_types: [
                            { name: 'Game Design', difficulty: 4 },
                            { name: 'Programming', difficulty: 5 },
                            { name: 'Art Creation', difficulty: 3 },
                            { name: 'Testing', difficulty: 2 }
                        ]
                    }
                },
                {
                    name: "Software Project", 
                    type: "software",
                    contribution_tracking: {
                        task_types: [
                            { name: 'Frontend Development', difficulty: 4 },
                            { name: 'Backend Development', difficulty: 5 },
                            { name: 'UI/UX Design', difficulty: 3 },
                            { name: 'Documentation', difficulty: 2 }
                        ]
                    }
                }
            ];

            for (const project of mockProjects) {
                try {
                    const formattedTable = formatTaskTypesTable(project.contribution_tracking.task_types);
                    
                    addResult(testName, 'success', `✅ ${project.name} task types processed successfully`);
                    addResult(testName, 'info', `<pre>${formattedTable}</pre>`);
                    
                    // Verify the table contains actual project task types
                    const hasProjectTaskTypes = project.contribution_tracking.task_types.every(taskType => 
                        formattedTable.includes(taskType.name)
                    );
                    
                    if (hasProjectTaskTypes) {
                        addResult(testName, 'success', `✅ All project task types found in generated table`);
                    } else {
                        addResult(testName, 'error', `❌ Some project task types missing from generated table`);
                    }
                    
                } catch (error) {
                    addResult(testName, 'error', `❌ Task type processing failed for ${project.name}: ${error.message}`);
                }
            }
        }

        async function testMilestoneFormatting() {
            const testName = "Milestone Formatting Test";
            
            const mockMilestones = [
                {
                    name: "Alpha Release",
                    description: "Initial playable version",
                    target_date: "2024-03-15"
                },
                {
                    name: "Beta Release", 
                    description: "Feature-complete version for testing",
                    target_date: "2024-06-01"
                }
            ];

            try {
                const formatted = formatMilestones(mockMilestones);
                addResult(testName, 'success', `✅ Milestones formatted successfully`);
                addResult(testName, 'info', `<pre>${formatted}</pre>`);
                
                // Verify milestone names are included
                const hasMilestoneNames = mockMilestones.every(milestone => 
                    formatted.includes(milestone.name)
                );
                
                if (hasMilestoneNames) {
                    addResult(testName, 'success', `✅ All milestone names found in formatted output`);
                } else {
                    addResult(testName, 'error', `❌ Some milestone names missing from formatted output`);
                }
                
            } catch (error) {
                addResult(testName, 'error', `❌ Milestone formatting failed: ${error.message}`);
            }
        }

        async function testConditionalLogic() {
            const testName = "Conditional Logic Test";
            
            const projectTypes = ['game', 'software', 'music', 'film', 'art'];
            
            for (const projectType of projectTypes) {
                try {
                    const variables = {
                        PROJECT_TYPE_GAME: projectType === 'game',
                        PROJECT_TYPE_SOFTWARE: projectType === 'software',
                        PROJECT_TYPE_MUSIC: projectType === 'music',
                        PROJECT_TYPE_FILM: projectType === 'film',
                        PROJECT_TYPE_ART: projectType === 'art'
                    };
                    
                    const activeConditions = Object.entries(variables)
                        .filter(([key, value]) => value === true)
                        .map(([key]) => key);
                    
                    addResult(testName, 'success', `✅ ${projectType.toUpperCase()} project type conditions: ${activeConditions.join(', ')}`);
                    
                } catch (error) {
                    addResult(testName, 'error', `❌ Conditional logic test failed for ${projectType}: ${error.message}`);
                }
            }
        }

        async function testVariableReplacement() {
            const testName = "Variable Replacement Test";
            
            const testTemplate = `
Project: {{PROJECT_NAME}}
Type: {{PROJECT_TYPE}}
{{#IF PROJECT_TYPE_SOFTWARE}}
This is a software project.
{{/IF}}
{{#IF PROJECT_TYPE_GAME}}
This is a game project.
{{/IF}}
            `;
            
            const variables = {
                PROJECT_NAME: "Test Project",
                PROJECT_TYPE: "Software",
                PROJECT_TYPE_SOFTWARE: true,
                PROJECT_TYPE_GAME: false
            };
            
            try {
                let processed = testTemplate;
                
                // Replace variables
                Object.entries(variables).forEach(([key, value]) => {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    processed = processed.replace(regex, String(value));
                });
                
                // Process conditionals
                const conditionalRegex = /{{#IF\s+([^}]+)}}([\s\S]*?){{\/IF}}/g;
                processed = processed.replace(conditionalRegex, (match, condition, content) => {
                    const conditionValue = variables[condition.trim()];
                    return (conditionValue === true || conditionValue === 'true') ? content : '';
                });
                
                addResult(testName, 'success', `✅ Variable replacement test completed`);
                addResult(testName, 'info', `<pre>${processed}</pre>`);
                
                // Verify replacements worked
                if (processed.includes('Test Project') && processed.includes('This is a software project') && !processed.includes('This is a game project')) {
                    addResult(testName, 'success', `✅ Variable replacement and conditional logic working correctly`);
                } else {
                    addResult(testName, 'error', `❌ Variable replacement or conditional logic failed`);
                }
                
            } catch (error) {
                addResult(testName, 'error', `❌ Variable replacement test failed: ${error.message}`);
            }
        }

        // Helper functions
        function formatTaskTypesTable(taskTypes) {
            if (!taskTypes || taskTypes.length === 0) {
                return `| Task Type | Difficulty Level | Points |
|-----------|-----------------|--------|
| Development | Medium | 5 |
| Design | Medium | 4 |
| Testing | Easy | 2 |
| Documentation | Easy | 3 |`;
            }

            const header = `| Task Type | Difficulty Level | Points |
|-----------|-----------------|--------|`;
            
            const rows = taskTypes.map(taskType => {
                const name = taskType.name || taskType;
                const difficulty = taskType.difficulty || 3;
                const difficultyLabel = difficulty <= 2 ? 'Easy' : difficulty <= 4 ? 'Medium' : 'Hard';
                const points = difficulty;
                return `| ${name} | ${difficultyLabel} | ${points} |`;
            }).join('\n');

            return `${header}\n${rows}`;
        }

        function formatMilestones(milestones) {
            if (!milestones || milestones.length === 0) {
                return 'Milestones to be defined during project planning';
            }

            return milestones.map((milestone, index) => {
                const name = milestone.name || milestone.title || `Milestone ${index + 1}`;
                const description = milestone.description || 'Description to be defined';
                const deadline = milestone.target_date ? 
                    new Date(milestone.target_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    }) : 'Date TBD';
                
                return `${index + 1}. **${name}** (${deadline})\n   ${description}`;
            }).join('\n\n');
        }

        function addResult(testName, type, message) {
            testResults.push({ testName, type, message, timestamp: new Date() });
            displayResults();
        }

        function displayResults() {
            const resultsDiv = document.getElementById('results');
            const groupedResults = {};
            
            testResults.forEach(result => {
                if (!groupedResults[result.testName]) {
                    groupedResults[result.testName] = [];
                }
                groupedResults[result.testName].push(result);
            });
            
            let html = '';
            Object.entries(groupedResults).forEach(([testName, results]) => {
                html += `<div class="container">
                    <h2>${testName}</h2>`;
                
                results.forEach(result => {
                    html += `<div class="test-section ${result.type}">
                        ${result.message}
                    </div>`;
                });
                
                html += '</div>';
            });
            
            resultsDiv.innerHTML = html;
        }

        function displaySummary() {
            const totalTests = testResults.length;
            const successCount = testResults.filter(r => r.type === 'success').length;
            const errorCount = testResults.filter(r => r.type === 'error').length;
            const warningCount = testResults.filter(r => r.type === 'warning').length;
            
            const summaryHtml = `
                <div class="container">
                    <h2>📊 Test Summary</h2>
                    <div class="test-section info">
                        <strong>Total Tests:</strong> ${totalTests}<br>
                        <strong>✅ Passed:</strong> ${successCount}<br>
                        <strong>❌ Failed:</strong> ${errorCount}<br>
                        <strong>⚠️ Warnings:</strong> ${warningCount}<br>
                        <strong>Success Rate:</strong> ${((successCount / totalTests) * 100).toFixed(1)}%
                    </div>
                </div>
            `;
            
            document.getElementById('results').innerHTML = summaryHtml + document.getElementById('results').innerHTML;
        }

        function clearResults() {
            testResults = [];
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
