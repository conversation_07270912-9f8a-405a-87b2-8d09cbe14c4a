import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Textarea, Select, SelectItem, Badge, Avatar } from '@heroui/react';
import { MessageSquare, Star, ThumbsUp, ThumbsDown, Send } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const FeedbackSystem = () => {
  const { currentUser } = useAuth();
  const [feedback, setFeedback] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newFeedback, setNewFeedback] = useState({
    type: 'general',
    category: 'feature_request',
    title: '',
    description: '',
    rating: 5
  });
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);

  useEffect(() => {
    loadFeedback();
  }, [currentUser]);

  const loadFeedback = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('user_feedback')
        .select(`
          *,
          users (
            id,
            display_name,
            avatar_url
          ),
          projects (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      setFeedback(data || getMockFeedback());

    } catch (error) {
      console.error('Error loading feedback:', error);
      setFeedback(getMockFeedback());
    } finally {
      setLoading(false);
    }
  };

  const getMockFeedback = () => [
    {
      id: 'feedback-1',
      type: 'feature_request',
      category: 'ui_ux',
      title: 'Dark Mode Support',
      description: 'Would love to have a dark mode option for better night-time usage.',
      rating: 4,
      status: 'open',
      user_id: 'user-1',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      upvotes: 12,
      downvotes: 1,
      users: {
        id: 'user-1',
        display_name: 'John Developer',
        avatar_url: null
      }
    },
    {
      id: 'feedback-2',
      type: 'bug_report',
      category: 'functionality',
      title: 'Timer Not Saving',
      description: 'The time tracking timer resets when switching between tabs.',
      rating: 2,
      status: 'in_progress',
      user_id: 'user-2',
      created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      upvotes: 8,
      downvotes: 0,
      users: {
        id: 'user-2',
        display_name: 'Sarah Tester',
        avatar_url: null
      }
    },
    {
      id: 'feedback-3',
      type: 'general',
      category: 'performance',
      title: 'Great Platform!',
      description: 'Really enjoying the collaboration features. The project management tools are intuitive.',
      rating: 5,
      status: 'closed',
      user_id: 'user-3',
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      upvotes: 15,
      downvotes: 0,
      users: {
        id: 'user-3',
        display_name: 'Mike Manager',
        avatar_url: null
      }
    }
  ];

  const submitFeedback = async () => {
    try {
      if (!newFeedback.title.trim() || !newFeedback.description.trim()) {
        toast.error('Please fill in all required fields');
        return;
      }

      const { error } = await supabase
        .from('user_feedback')
        .insert({
          ...newFeedback,
          user_id: currentUser.id,
          status: 'open',
          upvotes: 0,
          downvotes: 0
        });

      if (error) throw error;

      toast.success('Feedback submitted successfully!');
      setNewFeedback({
        type: 'general',
        category: 'feature_request',
        title: '',
        description: '',
        rating: 5
      });
      setShowFeedbackForm(false);
      loadFeedback();

    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback');
    }
  };

  const voteFeedback = async (feedbackId, voteType) => {
    try {
      // In a real implementation, this would handle vote logic
      toast.success(`Vote ${voteType} recorded`);
    } catch (error) {
      console.error('Error voting on feedback:', error);
      toast.error('Failed to record vote');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'warning';
      case 'in_progress': return 'primary';
      case 'closed': return 'success';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'ui_ux': return '🎨';
      case 'functionality': return '⚙️';
      case 'performance': return '⚡';
      case 'feature_request': return '💡';
      case 'bug_report': return '🐛';
      default: return '💬';
    }
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Feedback System</h2>
            <p className="text-white/70">Share your thoughts and help improve the platform</p>
          </div>
          <Button
            color="primary"
            startContent={<MessageSquare size={16} />}
            onClick={() => setShowFeedbackForm(!showFeedbackForm)}
          >
            Give Feedback
          </Button>
        </div>
      </div>

      {/* Feedback Form */}
      {showFeedbackForm && (
        <Card className="mb-6">
          <CardHeader>
            <h3 className="text-lg font-semibold">Submit Feedback</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Type"
                selectedKeys={[newFeedback.type]}
                onSelectionChange={(keys) => setNewFeedback(prev => ({ ...prev, type: Array.from(keys)[0] }))}
              >
                <SelectItem key="general">General Feedback</SelectItem>
                <SelectItem key="feature_request">Feature Request</SelectItem>
                <SelectItem key="bug_report">Bug Report</SelectItem>
                <SelectItem key="improvement">Improvement</SelectItem>
              </Select>

              <Select
                label="Category"
                selectedKeys={[newFeedback.category]}
                onSelectionChange={(keys) => setNewFeedback(prev => ({ ...prev, category: Array.from(keys)[0] }))}
              >
                <SelectItem key="ui_ux">UI/UX</SelectItem>
                <SelectItem key="functionality">Functionality</SelectItem>
                <SelectItem key="performance">Performance</SelectItem>
                <SelectItem key="feature_request">Feature Request</SelectItem>
                <SelectItem key="bug_report">Bug Report</SelectItem>
              </Select>
            </div>

            <input
              type="text"
              placeholder="Feedback title..."
              value={newFeedback.title}
              onChange={(e) => setNewFeedback(prev => ({ ...prev, title: e.target.value }))}
              className="w-full p-3 border rounded-lg"
            />

            <Textarea
              placeholder="Describe your feedback in detail..."
              value={newFeedback.description}
              onChange={(e) => setNewFeedback(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
            />

            <div className="flex items-center space-x-2">
              <span className="text-sm">Rating:</span>
              <div className="flex space-x-1">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    size={20}
                    className={`cursor-pointer ${i < newFeedback.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                    onClick={() => setNewFeedback(prev => ({ ...prev, rating: i + 1 }))}
                  />
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                color="primary"
                startContent={<Send size={16} />}
                onClick={submitFeedback}
              >
                Submit Feedback
              </Button>
              <Button
                variant="flat"
                onClick={() => setShowFeedbackForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Feedback List */}
      <div className="space-y-4">
        {feedback.map((item) => (
          <Card key={item.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{getCategoryIcon(item.category)}</div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">{item.title}</h3>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge 
                    color={getStatusColor(item.status)} 
                    variant="flat" 
                    size="sm"
                  >
                    {item.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Avatar
                      src={item.users?.avatar_url}
                      name={item.users?.display_name}
                      size="sm"
                    />
                    <span className="text-sm text-gray-600">{item.users?.display_name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {renderStars(item.rating)}
                  </div>
                  <span className="text-sm text-gray-500">
                    {new Date(item.created_at).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="flat"
                    startContent={<ThumbsUp size={14} />}
                    onClick={() => voteFeedback(item.id, 'upvote')}
                  >
                    {item.upvotes}
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    startContent={<ThumbsDown size={14} />}
                    onClick={() => voteFeedback(item.id, 'downvote')}
                  >
                    {item.downvotes}
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default FeedbackSystem;
