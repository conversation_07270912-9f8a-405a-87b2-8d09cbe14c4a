import React, { useState, useEffect, useRef, useContext } from 'react';
import { Card, CardBody, Button, Progress, Chip, Spinner } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import youtubeService from '../../services/youtubeService';
import { toast } from 'react-hot-toast';

/**
 * YouTube Video Player Component
 * 
 * Features:
 * - Embedded YouTube video playback
 * - Progress tracking and completion detection
 * - Automatic progress saving
 * - Video information display
 * - Responsive design
 * - Integration with learning system
 */
const YouTubePlayer = ({ 
  videoId, 
  videoData, 
  onProgress, 
  onComplete, 
  autoPlay = false,
  showControls = true,
  className = '' 
}) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const playerRef = useRef(null);
  const progressIntervalRef = useRef(null);
  const lastSavedProgress = useRef(0);

  // Initialize YouTube player
  useEffect(() => {
    if (!videoId) return;

    // Load YouTube IFrame API
    if (!window.YT) {
      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      window.onYouTubeIframeAPIReady = initializePlayer;
    } else {
      initializePlayer();
    }

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [videoId]);

  const initializePlayer = () => {
    try {
      playerRef.current = new window.YT.Player(`youtube-player-${videoId}`, {
        height: '315',
        width: '560',
        videoId: videoId,
        playerVars: {
          autoplay: autoPlay ? 1 : 0,
          controls: showControls ? 1 : 0,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          fs: 1,
          cc_load_policy: 1,
          iv_load_policy: 3,
          autohide: 0
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
          onError: onPlayerError
        }
      });
    } catch (error) {
      console.error('Error initializing YouTube player:', error);
      setError('Failed to load video player');
      setLoading(false);
    }
  };

  const onPlayerReady = (event) => {
    setLoading(false);
    setDuration(event.target.getDuration());
    
    // Start progress tracking
    startProgressTracking();
  };

  const onPlayerStateChange = (event) => {
    const playerState = event.data;
    
    switch (playerState) {
      case window.YT.PlayerState.PLAYING:
        setIsPlaying(true);
        startProgressTracking();
        break;
      case window.YT.PlayerState.PAUSED:
        setIsPlaying(false);
        saveProgress();
        break;
      case window.YT.PlayerState.ENDED:
        setIsPlaying(false);
        handleVideoComplete();
        break;
      default:
        break;
    }
  };

  const onPlayerError = (event) => {
    console.error('YouTube player error:', event.data);
    setError('Error loading video');
    setLoading(false);
  };

  const startProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    progressIntervalRef.current = setInterval(() => {
      if (playerRef.current && typeof playerRef.current.getCurrentTime === 'function') {
        const current = playerRef.current.getCurrentTime();
        const total = playerRef.current.getDuration();
        
        if (total > 0) {
          const progressPercent = Math.round((current / total) * 100);
          setCurrentTime(current);
          setProgress(progressPercent);
          
          // Save progress every 30 seconds or 10% progress change
          if (Math.abs(progressPercent - lastSavedProgress.current) >= 10) {
            saveProgress(current, total, progressPercent);
            lastSavedProgress.current = progressPercent;
          }
          
          // Call progress callback
          if (onProgress) {
            onProgress({
              currentTime: current,
              duration: total,
              percentage: progressPercent
            });
          }
        }
      }
    }, 1000);
  };

  const saveProgress = async (current = currentTime, total = duration, percent = progress) => {
    if (!currentUser || !videoId) return;

    try {
      await youtubeService.trackProgress(currentUser.id, videoId, {
        title: videoData?.title || 'YouTube Video',
        currentTime: current,
        duration: total,
        percentage: percent,
        timeSpent: Math.round(current / 60) // Convert to minutes
      });
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };

  const handleVideoComplete = async () => {
    setIsCompleted(true);
    setProgress(100);
    
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    // Save completion
    await saveProgress(duration, duration, 100);
    
    // Call completion callback
    if (onComplete) {
      onComplete({
        videoId,
        completedAt: new Date().toISOString(),
        totalTime: duration
      });
    }

    toast.success('Video completed! 🎉');
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSeek = (percentage) => {
    if (playerRef.current && duration > 0) {
      const seekTime = (percentage / 100) * duration;
      playerRef.current.seekTo(seekTime);
    }
  };

  if (error) {
    return (
      <Card className={`youtube-player-error ${className}`}>
        <CardBody className="text-center p-8">
          <div className="text-danger mb-4">
            <i className="bi bi-exclamation-triangle text-4xl"></i>
          </div>
          <h3 className="text-lg font-semibold mb-2">Video Error</h3>
          <p className="text-default-600 mb-4">{error}</p>
          <Button 
            color="primary" 
            variant="flat"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`youtube-player-container ${className}`}>
      <Card className="youtube-player-card">
        <CardBody className="p-0">
          {/* Video Player */}
          <div className="relative bg-black rounded-t-lg overflow-hidden">
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
                <Spinner size="lg" color="primary" />
              </div>
            )}
            
            <div 
              id={`youtube-player-${videoId}`}
              className="w-full aspect-video"
              style={{ minHeight: '315px' }}
            />
            
            {isCompleted && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute inset-0 flex items-center justify-center bg-black/70 z-20"
              >
                <div className="text-center text-white">
                  <div className="text-6xl mb-4">🎉</div>
                  <h3 className="text-2xl font-bold mb-2">Video Completed!</h3>
                  <p className="text-lg">Great job finishing this lesson</p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Video Info and Progress */}
          <div className="p-4">
            {videoData && (
              <div className="mb-4">
                <h3 className="text-lg font-semibold mb-2 line-clamp-2">
                  {videoData.title}
                </h3>
                <div className="flex items-center gap-2 text-sm text-default-600 mb-2">
                  <Chip color="primary" size="sm" variant="flat">
                    YouTube
                  </Chip>
                  <span>{videoData.channelTitle}</span>
                  {videoData.duration && (
                    <span>• {videoData.duration} min</span>
                  )}
                </div>
                {videoData.description && (
                  <p className="text-sm text-default-600 line-clamp-3">
                    {videoData.description}
                  </p>
                )}
              </div>
            )}

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span className="text-default-600">Progress</span>
                <span className="font-medium">{progress}%</span>
              </div>
              
              <Progress 
                value={progress} 
                color={isCompleted ? "success" : "primary"}
                className="cursor-pointer"
                onClick={(e) => {
                  const rect = e.currentTarget.getBoundingClientRect();
                  const clickX = e.clientX - rect.left;
                  const percentage = (clickX / rect.width) * 100;
                  handleSeek(percentage);
                }}
              />
              
              <div className="flex justify-between items-center text-xs text-default-500">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Status Indicators */}
            <div className="flex items-center gap-2 mt-3">
              {isPlaying && (
                <Chip color="success" size="sm" variant="dot">
                  Playing
                </Chip>
              )}
              {isCompleted && (
                <Chip color="success" size="sm" variant="flat">
                  ✓ Completed
                </Chip>
              )}
              {progress > 0 && progress < 100 && (
                <Chip color="warning" size="sm" variant="flat">
                  In Progress
                </Chip>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default YouTubePlayer;
