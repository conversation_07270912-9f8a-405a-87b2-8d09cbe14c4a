import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Badge, Ta<PERSON>, Tab } from '@heroui/react';
import { Star, ThumbsUp, BookOpen, Users, Code, Lightbulb } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const BestPractices = () => {
  const { currentUser } = useAuth();
  const [practices, setPractices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [userVotes, setUserVotes] = useState({});

  useEffect(() => {
    loadBestPractices();
  }, []);

  const loadBestPractices = async () => {
    try {
      setLoading(true);

      // Load best practices
      const { data: practicesData, error: practicesError } = await supabase
        .from('learning_content')
        .select('*')
        .eq('content_type', 'best_practice')
        .eq('is_active', true)
        .order('vote_count', { ascending: false });

      if (practicesError) throw practicesError;

      // Load user votes
      const { data: votesData, error: votesError } = await supabase
        .from('content_votes')
        .select('content_id, vote_type')
        .eq('user_id', currentUser.id);

      if (votesError) throw votesError;

      // Create votes map
      const votesMap = {};
      votesData?.forEach(vote => {
        votesMap[vote.content_id] = vote.vote_type;
      });

      setPractices(practicesData || getStaticBestPractices());
      setUserVotes(votesMap);

    } catch (error) {
      console.error('Error loading best practices:', error);
      setPractices(getStaticBestPractices());
    } finally {
      setLoading(false);
    }
  };

  const getStaticBestPractices = () => [
    {
      id: 'code-review',
      title: 'Effective Code Review Process',
      description: 'Guidelines for conducting thorough and constructive code reviews',
      category: 'development',
      content: 'Always review code for functionality, readability, and maintainability. Provide specific, actionable feedback.',
      author: 'Platform Team',
      vote_count: 45,
      difficulty_level: 'intermediate',
      tags: ['code-review', 'development', 'quality']
    },
    {
      id: 'project-planning',
      title: 'Project Planning and Scope Management',
      description: 'Best practices for defining project scope and creating realistic timelines',
      category: 'management',
      content: 'Break down large projects into smaller, manageable tasks. Set clear milestones and deadlines.',
      author: 'Project Management Team',
      vote_count: 38,
      difficulty_level: 'beginner',
      tags: ['planning', 'management', 'scope']
    },
    {
      id: 'team-communication',
      title: 'Effective Team Communication',
      description: 'Strategies for maintaining clear communication in distributed teams',
      category: 'collaboration',
      content: 'Use asynchronous communication effectively. Document decisions and maintain regular check-ins.',
      author: 'Team Leads',
      vote_count: 52,
      difficulty_level: 'beginner',
      tags: ['communication', 'teamwork', 'remote']
    },
    {
      id: 'security-practices',
      title: 'Security Best Practices',
      description: 'Essential security considerations for all projects',
      category: 'security',
      content: 'Never commit sensitive data. Use environment variables and follow the principle of least privilege.',
      author: 'Security Team',
      vote_count: 67,
      difficulty_level: 'advanced',
      tags: ['security', 'data-protection', 'compliance']
    }
  ];

  const votePractice = async (practiceId, voteType) => {
    try {
      const { error } = await supabase
        .from('content_votes')
        .upsert({
          user_id: currentUser.id,
          content_id: practiceId,
          vote_type: voteType
        });

      if (error) throw error;

      // Update local state
      setUserVotes(prev => ({
        ...prev,
        [practiceId]: voteType
      }));

      // Refresh practices to get updated vote counts
      loadBestPractices();

    } catch (error) {
      console.error('Error voting on practice:', error);
    }
  };

  const getFilteredPractices = () => {
    if (selectedCategory === 'all') return practices;
    return practices.filter(practice => practice.category === selectedCategory);
  };

  const getCategories = () => {
    const categories = [...new Set(practices.map(p => p.category))];
    return ['all', ...categories];
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'development': return <Code size={16} />;
      case 'management': return <Users size={16} />;
      case 'collaboration': return <Users size={16} />;
      case 'security': return <Star size={16} />;
      default: return <Lightbulb size={16} />;
    }
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'danger';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const filteredPractices = getFilteredPractices();

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Best Practices</h2>
        <p className="text-white/70">Learn from community-approved best practices and guidelines</p>
      </div>

      <Tabs
        selectedKey={selectedCategory}
        onSelectionChange={setSelectedCategory}
        className="mb-6"
      >
        {getCategories().map(category => (
          <Tab
            key={category}
            title={
              <div className="flex items-center space-x-2">
                {category === 'all' ? <BookOpen size={16} /> : getCategoryIcon(category)}
                <span className="capitalize">{category === 'all' ? 'All' : category}</span>
              </div>
            }
          />
        ))}
      </Tabs>

      {filteredPractices.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <Lightbulb size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Best Practices Found</h3>
            <p className="text-gray-600">No practices available in this category.</p>
          </CardBody>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredPractices.map((practice) => {
            const userVote = userVotes[practice.id];
            
            return (
              <Card key={practice.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {getCategoryIcon(practice.category)}
                        <h3 className="text-lg font-semibold">{practice.title}</h3>
                        <Badge 
                          color={getDifficultyColor(practice.difficulty_level)} 
                          variant="flat" 
                          size="sm"
                        >
                          {practice.difficulty_level}
                        </Badge>
                      </div>
                      <p className="text-gray-600 text-sm">{practice.description}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardBody className="pt-2">
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm">{practice.content}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>By {practice.author}</span>
                      <div className="flex flex-wrap gap-1">
                        {practice.tags?.map(tag => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">{practice.vote_count} votes</span>
                      <Button
                        size="sm"
                        color={userVote === 'upvote' ? "success" : "default"}
                        variant={userVote === 'upvote' ? "solid" : "flat"}
                        startContent={<ThumbsUp size={14} />}
                        onClick={() => votePractice(practice.id, 'upvote')}
                      >
                        Helpful
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default BestPractices;
