import React, { useState, useEffect, useContext, useRef } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Input, Avatar, Chip,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  Dropdown, DropdownTrigger, DropdownMenu, DropdownItem,
  useDisclosure, Spinner
} from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Simple Chat System Component
 * 
 * Provides basic messaging functionality between friends:
 * - View conversations with friends
 * - Send and receive messages
 * - Real-time message updates
 * - Message history
 */
const SimpleChatSystem = ({ isOpen, onClose }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [friends, setFriends] = useState([]);
  const [selectedFriend, setSelectedFriend] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const messagesEndRef = useRef(null);

  // Load friends and conversations
  useEffect(() => {
    if (isOpen && currentUser) {
      loadFriends();
    }
  }, [isOpen, currentUser]);

  // Load messages when friend is selected
  useEffect(() => {
    if (selectedFriend) {
      loadMessages(selectedFriend.friend.id);
      // Set up real-time subscription for messages
      const subscription = subscribeToMessages(selectedFriend.friend.id);
      return () => {
        if (subscription) {
          supabase.removeChannel(subscription);
        }
      };
    }
  }, [selectedFriend]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load user's friends
  const loadFriends = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('user_allies')
        .select(`
          *,
          ally:auth.users!user_allies_ally_id_fkey(
            id,
            email,
            user_metadata
          ),
          user:auth.users!user_allies_user_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .or(`user_id.eq.${currentUser.id},ally_id.eq.${currentUser.id}`)
        .eq('status', 'accepted');

      if (error) throw error;

      // Process friends data
      const processedFriends = data?.map(connection => {
        const isUserInitiator = connection.user_id === currentUser.id;
        const friend = isUserInitiator ? connection.ally : connection.user;
        return {
          ...connection,
          friend,
          friendName: friend?.user_metadata?.display_name || friend?.email,
          friendAvatar: friend?.user_metadata?.avatar_url,
          lastMessage: null,
          unreadCount: 0
        };
      }) || [];

      setFriends(processedFriends);
      
      // Select first friend if none selected
      if (processedFriends.length > 0 && !selectedFriend) {
        setSelectedFriend(processedFriends[0]);
      }

    } catch (error) {
      console.error('Error loading friends:', error);
      toast.error('Failed to load friends');
    } finally {
      setLoading(false);
    }
  };

  // Load messages for a conversation
  const loadMessages = async (friendId) => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:auth.users!messages_sender_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .or(`and(sender_id.eq.${currentUser.id},recipient_id.eq.${friendId}),and(sender_id.eq.${friendId},recipient_id.eq.${currentUser.id})`)
        .order('created_at', { ascending: true })
        .limit(100);

      if (error) throw error;
      
      setMessages(data || []);

    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    }
  };

  // Subscribe to real-time messages
  const subscribeToMessages = (friendId) => {
    const channel = supabase
      .channel(`messages-${currentUser.id}-${friendId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `or(and(sender_id.eq.${currentUser.id},recipient_id.eq.${friendId}),and(sender_id.eq.${friendId},recipient_id.eq.${currentUser.id}))`
        },
        (payload) => {
          setMessages(prev => [...prev, payload.new]);
        }
      )
      .subscribe();

    return channel;
  };

  // Send message
  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedFriend || sendingMessage) return;

    try {
      setSendingMessage(true);

      const messageData = {
        sender_id: currentUser.id,
        recipient_id: selectedFriend.friend.id,
        content: newMessage.trim(),
        message_type: 'text'
      };

      const { error } = await supabase
        .from('messages')
        .insert([messageData]);

      if (error) throw error;

      setNewMessage('');

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Format message time
  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  if (loading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="4xl">
        <ModalContent>
          <ModalBody className="py-8">
            <div className="text-center">
              <Spinner size="lg" />
              <p className="mt-2">Loading chat...</p>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="5xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Messages</h2>
          <p className="text-default-600 font-normal">
            Chat with your friends and collaborators
          </p>
        </ModalHeader>
        
        <ModalBody className="p-0">
          <div className="flex h-[600px]">
            {/* Friends Sidebar */}
            <div className="w-80 border-r border-default-200 flex flex-col">
              <div className="p-4 border-b border-default-200">
                <h3 className="font-semibold">Conversations</h3>
              </div>
              
              <div className="flex-1 overflow-y-auto">
                {friends.length === 0 ? (
                  <div className="text-center py-8 text-default-600">
                    <i className="bi bi-people text-4xl mb-2 block"></i>
                    <p className="text-sm">No friends to chat with</p>
                    <p className="text-xs">Add friends to start messaging</p>
                  </div>
                ) : (
                  <div className="space-y-1 p-2">
                    {friends.map((friend) => (
                      <div
                        key={friend.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          selectedFriend?.id === friend.id
                            ? 'bg-primary/10 border border-primary/20'
                            : 'hover:bg-default-100'
                        }`}
                        onClick={() => setSelectedFriend(friend)}
                      >
                        <div className="flex items-center gap-3">
                          <Avatar
                            src={friend.friendAvatar}
                            name={friend.friendName}
                            size="md"
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium truncate">
                              {friend.friendName}
                            </h4>
                            <p className="text-xs text-default-500 truncate">
                              {friend.lastMessage || 'Start a conversation'}
                            </p>
                          </div>
                          {friend.unreadCount > 0 && (
                            <Chip size="sm" color="primary">
                              {friend.unreadCount}
                            </Chip>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {selectedFriend ? (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-default-200 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar
                        src={selectedFriend.friendAvatar}
                        name={selectedFriend.friendName}
                        size="md"
                      />
                      <div>
                        <h3 className="font-semibold">{selectedFriend.friendName}</h3>
                        <p className="text-xs text-default-500">
                          Connected since {new Date(selectedFriend.accepted_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    
                    <Dropdown>
                      <DropdownTrigger>
                        <Button variant="flat" isIconOnly size="sm">
                          <i className="bi bi-three-dots"></i>
                        </Button>
                      </DropdownTrigger>
                      <DropdownMenu>
                        <DropdownItem key="profile">
                          <i className="bi bi-person mr-2"></i>
                          View Profile
                        </DropdownItem>
                        <DropdownItem key="clear">
                          <i className="bi bi-trash mr-2"></i>
                          Clear Chat
                        </DropdownItem>
                      </DropdownMenu>
                    </Dropdown>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    <AnimatePresence>
                      {messages.map((message) => {
                        const isOwnMessage = message.sender_id === currentUser.id;
                        return (
                          <motion.div
                            key={message.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`max-w-[70%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                              <div
                                className={`p-3 rounded-lg ${
                                  isOwnMessage
                                    ? 'bg-primary text-white'
                                    : 'bg-default-100 text-default-900'
                                }`}
                              >
                                <p className="text-sm">{message.content}</p>
                              </div>
                              <p className={`text-xs text-default-500 mt-1 ${
                                isOwnMessage ? 'text-right' : 'text-left'
                              }`}>
                                {formatMessageTime(message.created_at)}
                              </p>
                            </div>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                    
                    {messages.length === 0 && (
                      <div className="text-center py-8 text-default-600">
                        <i className="bi bi-chat-dots text-4xl mb-2 block"></i>
                        <p>No messages yet</p>
                        <p className="text-sm">Start the conversation!</p>
                      </div>
                    )}
                    
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t border-default-200">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Type a message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="flex-1"
                        disabled={sendingMessage}
                      />
                      <Button
                        color="primary"
                        onClick={sendMessage}
                        isLoading={sendingMessage}
                        isDisabled={!newMessage.trim()}
                      >
                        <i className="bi bi-send"></i>
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center text-default-600">
                  <div className="text-center">
                    <i className="bi bi-chat-square-text text-6xl mb-4 block opacity-50"></i>
                    <h3 className="text-lg font-semibold mb-2">Select a conversation</h3>
                    <p className="text-sm">Choose a friend from the sidebar to start chatting</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SimpleChatSystem;
