import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Debug Studio State', () => {
  test.beforeEach(async ({ page }) => {
    // Login with test credentials
    console.log('🔐 Logging in with test credentials...');
    await page.goto(`${PRODUCTION_URL}/login`);
    await page.waitForLoadState('networkidle');
    
    // Fill login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify successful login (redirects to dashboard)
    await expect(page).toHaveURL(/dashboard/);
    console.log('✅ Successfully logged in');
  });

  test('Check Current Studio State', async ({ page }) => {
    console.log('🔍 Checking current studio state...');
    
    // Navigate to project wizard
    await page.goto(`${PRODUCTION_URL}/project/wizard`);
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'debug-studio-state.png', fullPage: true });
    
    // Check if user has existing studios
    const studioCards = page.locator('[data-testid="studio-card"], .studio-card, [class*="studio"]');
    const studioCount = await studioCards.count();
    console.log(`📊 Found ${studioCount} studio cards`);
    
    // Check for "Create New Studio" button
    const createStudioButton = page.locator('button:has-text("Create New Studio"), button:has-text("Create Studio"), button:has-text("New Studio")');
    const createButtonVisible = await createStudioButton.isVisible();
    console.log(`🔘 Create Studio button visible: ${createButtonVisible}`);
    
    // Check for any text containing "studio" (case insensitive)
    const studioTexts = page.locator('text=/studio/i');
    const studioTextCount = await studioTexts.count();
    console.log(`📝 Found ${studioTextCount} elements containing "studio"`);
    
    // List all visible text on the page
    const allText = await page.locator('body').textContent();
    console.log('📄 Page content preview:', allText.substring(0, 500) + '...');
    
    // Check for specific legal compliance terms
    const legalTerms = ['Business Type', 'Legal Entity', 'Tax ID', 'EIN', 'Company Type'];
    for (const term of legalTerms) {
      const element = page.locator(`text=${term}`);
      const isVisible = await element.isVisible();
      console.log(`⚖️ "${term}" visible: ${isVisible}`);
    }
  });
});
