import { test, expect } from '@playwright/test';

/**
 * Studio and Project Creation Flow Testing
 * 
 * Tests the specific functionality we've been working on:
 * - Studio creation through project wizard
 * - Project creation workflow
 * - Database integration and error handling
 * - Visual confirmation of successful creation
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Studio and Project Creation Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Login using visual approach
    const emailField = page.locator('input[type="email"]').first();
    const passwordField = page.locator('input[type="password"]').first();
    
    if (await emailField.isVisible() && await passwordField.isVisible()) {
      await emailField.fill(TEST_USER.email);
      await passwordField.fill(TEST_USER.password);
      
      // Find visible login button
      const allButtons = await page.locator('button').all();
      for (const button of allButtons) {
        try {
          const isVisible = await button.isVisible();
          const text = await button.textContent();
          if (isVisible && text && text.toLowerCase().includes('sign')) {
            await button.click();
            await page.waitForLoadState('networkidle');
            break;
          }
        } catch (e) {
          // Continue
        }
      }
    }
  });

  test('Studio Creation Through Project Wizard', async ({ page }) => {
    console.log('🏢 Testing studio creation through project wizard...');

    // Navigate to Start page
    const startLink = page.locator('a:has-text("Start"), button:has-text("Start")').first();
    if (await startLink.isVisible()) {
      await startLink.click();
      await page.waitForLoadState('networkidle');
    }

    // Take screenshot of Start page
    await page.screenshot({ 
      path: 'test-results/start-page-before-creation.png', 
      fullPage: true 
    });

    // Look for project creation options
    const createButtons = [
      'button:has-text("Create Project")',
      'button:has-text("New Project")',
      'button:has-text("Create")',
      'a:has-text("Create Project")',
      'button:has-text("Start Project")'
    ];

    let projectWizardStarted = false;
    for (const selector of createButtons) {
      const button = page.locator(selector).first();
      if (await button.isVisible()) {
        console.log(`🎯 Clicking project creation button: ${selector}`);
        await button.click();
        await page.waitForLoadState('networkidle');
        projectWizardStarted = true;
        break;
      }
    }

    if (!projectWizardStarted) {
      console.error('❌ Could not find project creation button');
      
      // Debug: List all visible buttons
      const allButtons = await page.locator('button:visible').all();
      console.log('🔍 Available buttons:');
      for (const button of allButtons) {
        try {
          const text = await button.textContent();
          console.log(`  - "${text}"`);
        } catch (e) {
          // Skip
        }
      }
      return;
    }

    // Take screenshot of project wizard
    await page.screenshot({ 
      path: 'test-results/project-wizard-initial.png', 
      fullPage: true 
    });

    // Check if we're in the project wizard
    const pageContent = await page.textContent('body');
    const isInWizard = pageContent.includes('wizard') || 
                      pageContent.includes('studio') || 
                      pageContent.includes('project') ||
                      page.url().includes('wizard') ||
                      page.url().includes('project');

    if (!isInWizard) {
      console.error('❌ Not in project wizard after clicking create button');
      console.log(`Current URL: ${page.url()}`);
      return;
    }

    console.log('✅ Successfully entered project wizard');

    // Look for studio creation fields
    const studioNameField = page.locator('input[placeholder*="studio" i], input[placeholder*="name" i], input[name*="name"]').first();
    
    if (await studioNameField.isVisible()) {
      console.log('🏢 Found studio name field, filling it...');
      const studioName = `Test Studio ${Date.now()}`;
      await studioNameField.fill(studioName);
      
      // Look for business type selection
      const businessTypeOptions = await page.locator('select, [role="combobox"], input[type="radio"]').all();
      for (const option of businessTypeOptions) {
        try {
          if (await option.isVisible()) {
            await option.click();
            await page.waitForTimeout(500);
            
            // Try to select individual option
            const individualOption = page.locator('option:has-text("Individual"), [role="option"]:has-text("Individual"), input[value*="individual"]').first();
            if (await individualOption.isVisible()) {
              await individualOption.click();
              console.log('✅ Selected individual business type');
            }
            break;
          }
        } catch (e) {
          // Continue
        }
      }

      // Look for continue/next button
      const continueButtons = [
        'button:has-text("Continue")',
        'button:has-text("Next")',
        'button:has-text("Create Studio")',
        'button:has-text("Create")',
        'button[type="submit"]'
      ];

      let continued = false;
      for (const selector of continueButtons) {
        const button = page.locator(selector).first();
        if (await button.isVisible()) {
          console.log(`🎯 Clicking continue button: ${selector}`);
          
          // Take screenshot before clicking
          await page.screenshot({ 
            path: 'test-results/before-studio-creation.png', 
            fullPage: true 
          });
          
          await button.click();
          await page.waitForLoadState('networkidle');
          continued = true;
          
          // Take screenshot after clicking
          await page.screenshot({ 
            path: 'test-results/after-studio-creation.png', 
            fullPage: true 
          });
          
          break;
        }
      }

      if (continued) {
        console.log('✅ Studio creation step completed');
        
        // Check for any error messages
        const errorMessages = await page.locator('[class*="error"], [class*="alert"], text="Error"').all();
        if (errorMessages.length > 0) {
          console.error('❌ Found error messages after studio creation:');
          for (const error of errorMessages) {
            try {
              const text = await error.textContent();
              console.error(`  - ${text}`);
            } catch (e) {
              // Skip
            }
          }
        } else {
          console.log('✅ No error messages found');
        }
      } else {
        console.error('❌ Could not find continue button');
      }
    } else {
      console.log('ℹ️ No studio name field found - may already have studio or different flow');
    }

    // Check final state
    const finalContent = await page.textContent('body');
    const finalUrl = page.url();
    
    console.log(`📍 Final URL: ${finalUrl}`);
    console.log(`📄 Page contains project content: ${finalContent.includes('project') ? '✅' : '❌'}`);
    console.log(`📄 Page contains studio content: ${finalContent.includes('studio') ? '✅' : '❌'}`);

    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/studio-creation-final.png', 
      fullPage: true 
    });

    console.log('🏢 Studio creation test completed');
  });

  test('Track Page Kanban Functionality', async ({ page }) => {
    console.log('📋 Testing Track page kanban functionality...');

    // Navigate to Track page
    const trackLink = page.locator('a:has-text("Track"), button:has-text("Track")').first();
    if (await trackLink.isVisible()) {
      await trackLink.click();
      await page.waitForLoadState('networkidle');
    }

    // Take screenshot of Track page
    await page.screenshot({ 
      path: 'test-results/track-page-detailed.png', 
      fullPage: true 
    });

    const pageContent = await page.textContent('body');
    
    // Check for kanban-related content
    const kanbanKeywords = ['kanban', 'task', 'project', 'board', 'column', 'todo', 'doing', 'done'];
    const foundKeywords = kanbanKeywords.filter(keyword => 
      pageContent.toLowerCase().includes(keyword)
    );
    
    console.log(`🔍 Found kanban keywords: ${foundKeywords.join(', ')}`);
    
    if (foundKeywords.length === 0) {
      console.error('❌ No kanban-related content found on Track page');
      console.log('📄 Page content preview:', pageContent.substring(0, 500));
    } else {
      console.log('✅ Track page has kanban-related content');
    }

    // Look for interactive elements that might be kanban-related
    const interactiveElements = await page.locator('button:visible, [role="button"]:visible').all();
    console.log(`🎯 Found ${interactiveElements.length} interactive elements on Track page`);

    for (const element of interactiveElements.slice(0, 5)) {
      try {
        const text = await element.textContent();
        if (text && (text.includes('Add') || text.includes('Create') || text.includes('New'))) {
          console.log(`🎯 Found potential task creation button: "${text}"`);
        }
      } catch (e) {
        // Skip
      }
    }

    console.log('📋 Track page kanban test completed');
  });
});
