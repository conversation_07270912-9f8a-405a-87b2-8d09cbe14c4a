import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Tabs, Tab, Chip, Badge,
  Avatar, Progress, Divider, Spinner
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Profile System Verification Component
 * 
 * Verifies that the profile system is working correctly:
 * - User profile data display
 * - Skills and achievements
 * - Learning progress tracking
 * - Social connections
 * - Profile customization
 */
const ProfileSystemVerification = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [skillsData, setSkillsData] = useState([]);
  const [learningProgress, setLearningProgress] = useState([]);
  const [socialConnections, setSocialConnections] = useState([]);
  const [achievements, setAchievements] = useState([]);
  
  const [systemStatus, setSystemStatus] = useState({
    profileTable: false,
    skillsTable: false,
    progressTable: false,
    connectionsTable: false,
    achievementsTable: false,
    dataIntegrity: false
  });

  useEffect(() => {
    if (currentUser) {
      verifyProfileSystem();
    }
  }, [currentUser]);

  const verifyProfileSystem = async () => {
    try {
      setLoading(true);
      
      // Test and load profile data
      const profileStatus = await loadProfileData();
      
      // Test and load skills data
      const skillsStatus = await loadSkillsData();
      
      // Test and load learning progress
      const progressStatus = await loadLearningProgress();
      
      // Test and load social connections
      const connectionsStatus = await loadSocialConnections();
      
      // Test and load achievements
      const achievementsStatus = await loadAchievements();
      
      // Check data integrity
      const dataIntegrity = checkDataIntegrity();
      
      setSystemStatus({
        profileTable: profileStatus,
        skillsTable: skillsStatus,
        progressTable: progressStatus,
        connectionsTable: connectionsStatus,
        achievementsTable: achievementsStatus,
        dataIntegrity
      });
      
    } catch (error) {
      console.error('Error verifying profile system:', error);
      toast.error('Failed to verify profile system');
    } finally {
      setLoading(false);
    }
  };

  const loadProfileData = async () => {
    try {
      // Check if user_profiles table exists and is accessible
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Profile table error:', error);
        return false;
      }

      // If no profile exists, create a basic one for testing
      if (!data) {
        const { data: newProfile, error: createError } = await supabase
          .from('user_profiles')
          .insert([{
            user_id: currentUser.id,
            professional_title: 'Software Developer',
            bio: 'Learning and growing in the tech industry',
            availability_status: 'available',
            profile_visibility: 'public'
          }])
          .select()
          .single();

        if (createError) {
          console.error('Error creating profile:', createError);
          return false;
        }
        
        setProfileData(newProfile);
      } else {
        setProfileData(data);
      }
      
      return true;
    } catch (error) {
      console.error('Error loading profile data:', error);
      return false;
    }
  };

  const loadSkillsData = async () => {
    try {
      const { data, error } = await supabase
        .from('user_skills')
        .select('*')
        .eq('user_id', currentUser.id);

      if (error) {
        console.error('Skills table error:', error);
        return false;
      }

      setSkillsData(data || []);
      return true;
    } catch (error) {
      console.error('Error loading skills data:', error);
      return false;
    }
  };

  const loadLearningProgress = async () => {
    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('last_accessed_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Learning progress table error:', error);
        return false;
      }

      setLearningProgress(data || []);
      return true;
    } catch (error) {
      console.error('Error loading learning progress:', error);
      return false;
    }
  };

  const loadSocialConnections = async () => {
    try {
      const { data, error } = await supabase
        .from('user_allies')
        .select(`
          *,
          ally:auth.users!user_allies_ally_id_fkey(
            id,
            email,
            user_metadata
          ),
          user:auth.users!user_allies_user_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .or(`user_id.eq.${currentUser.id},ally_id.eq.${currentUser.id}`)
        .eq('status', 'accepted');

      if (error) {
        console.error('Social connections table error:', error);
        return false;
      }

      setSocialConnections(data || []);
      return true;
    } catch (error) {
      console.error('Error loading social connections:', error);
      return false;
    }
  };

  const loadAchievements = async () => {
    try {
      // Check if achievements table exists
      const { data, error } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', currentUser.id);

      if (error && error.code !== '42P01') { // 42P01 = table doesn't exist
        console.error('Achievements table error:', error);
        return false;
      }

      setAchievements(data || []);
      return true;
    } catch (error) {
      console.error('Error loading achievements:', error);
      return false;
    }
  };

  const checkDataIntegrity = () => {
    // Check if user has basic profile data
    const hasBasicProfile = profileData && profileData.user_id === currentUser.id;
    
    // Check if user metadata is accessible
    const hasUserMetadata = currentUser.user_metadata || currentUser.email;
    
    // Check if skills are properly linked
    const skillsLinked = skillsData.every(skill => skill.user_id === currentUser.id);
    
    // Check if learning progress is properly linked
    const progressLinked = learningProgress.every(progress => progress.user_id === currentUser.id);
    
    return hasBasicProfile && hasUserMetadata && skillsLinked && progressLinked;
  };

  const getStatusColor = (status) => status ? 'success' : 'danger';
  const getStatusIcon = (status) => status ? 'bi-check-circle' : 'bi-x-circle';

  const calculateProfileCompleteness = () => {
    if (!profileData) return 0;
    
    const fields = [
      profileData.professional_title,
      profileData.bio,
      profileData.location,
      profileData.website_url,
      profileData.profile_photo_url,
      skillsData.length > 0,
      learningProgress.length > 0
    ];
    
    const completedFields = fields.filter(field => field).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2">Verifying profile system...</span>
      </div>
    );
  }

  return (
    <div className="profile-system-verification space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Profile System Verification</h2>
          <p className="text-default-600">Testing profile features and data integrity</p>
        </div>
        
        <Button color="primary" onClick={verifyProfileSystem}>
          <i className="bi bi-arrow-clockwise mr-1"></i>
          Refresh
        </Button>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">System Status</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.profileTable)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.profileTable)}></i>}
              >
                Profile Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.skillsTable)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.skillsTable)}></i>}
              >
                Skills Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.progressTable)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.progressTable)}></i>}
              >
                Progress Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.connectionsTable)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.connectionsTable)}></i>}
              >
                Connections Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.achievementsTable)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.achievementsTable)}></i>}
              >
                Achievements Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getStatusColor(systemStatus.dataIntegrity)}
                variant="flat"
                startContent={<i className={getStatusIcon(systemStatus.dataIntegrity)}></i>}
              >
                Data Integrity
              </Chip>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Profile Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <h3 className="text-lg font-semibold">Profile Overview</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="text-center">
              <Avatar
                src={profileData?.profile_photo_url || currentUser?.user_metadata?.avatar_url}
                name={profileData?.professional_title || currentUser?.user_metadata?.display_name || currentUser?.email}
                size="lg"
                className="mb-3"
              />
              <h4 className="font-semibold">
                {currentUser?.user_metadata?.display_name || currentUser?.email}
              </h4>
              <p className="text-sm text-default-600">
                {profileData?.professional_title || 'No title set'}
              </p>
            </div>

            <Divider />

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Profile Completeness</span>
                <span className="text-sm">{calculateProfileCompleteness()}%</span>
              </div>
              <Progress
                value={calculateProfileCompleteness()}
                color="primary"
                size="sm"
              />
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Bio:</span>
                <Chip size="sm" color={profileData?.bio ? 'success' : 'default'} variant="flat">
                  {profileData?.bio ? 'Set' : 'Missing'}
                </Chip>
              </div>
              <div className="flex justify-between">
                <span>Location:</span>
                <Chip size="sm" color={profileData?.location ? 'success' : 'default'} variant="flat">
                  {profileData?.location ? 'Set' : 'Missing'}
                </Chip>
              </div>
              <div className="flex justify-between">
                <span>Website:</span>
                <Chip size="sm" color={profileData?.website_url ? 'success' : 'default'} variant="flat">
                  {profileData?.website_url ? 'Set' : 'Missing'}
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Skills and Progress */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold">Skills & Learning Progress</h3>
          </CardHeader>
          <CardBody>
            <Tabs>
              <Tab key="skills" title={`Skills (${skillsData.length})`}>
                <div className="mt-4">
                  {skillsData.length === 0 ? (
                    <div className="text-center py-8 text-default-600">
                      <i className="bi bi-award text-4xl mb-2 block"></i>
                      <p>No skills recorded</p>
                      <p className="text-sm">Skills will appear here as you complete assessments</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {skillsData.map((skill) => (
                        <Card key={skill.id} className="border">
                          <CardBody className="p-3">
                            <div className="flex justify-between items-center mb-2">
                              <h4 className="font-medium">{skill.name}</h4>
                              <Chip size="sm" color="primary" variant="flat">
                                Level {skill.level}
                              </Chip>
                            </div>
                            <Progress
                              value={skill.proficiency_score || 0}
                              color="primary"
                              size="sm"
                              className="mb-2"
                            />
                            <p className="text-xs text-default-600">
                              Last updated: {new Date(skill.updated_at).toLocaleDateString()}
                            </p>
                          </CardBody>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </Tab>
              
              <Tab key="progress" title={`Learning (${learningProgress.length})`}>
                <div className="mt-4">
                  {learningProgress.length === 0 ? (
                    <div className="text-center py-8 text-default-600">
                      <i className="bi bi-book text-4xl mb-2 block"></i>
                      <p>No learning progress recorded</p>
                      <p className="text-sm">Start learning to see your progress here</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {learningProgress.map((progress) => (
                        <Card key={progress.id} className="border">
                          <CardBody className="p-3">
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex-1">
                                <h4 className="font-medium line-clamp-1">{progress.course_title}</h4>
                                <p className="text-sm text-default-600">
                                  {progress.course_provider} • {progress.time_spent_minutes || 0} minutes
                                </p>
                              </div>
                              <Chip
                                size="sm"
                                color={progress.status === 'completed' ? 'success' : 'warning'}
                                variant="flat"
                              >
                                {progress.completion_percentage}%
                              </Chip>
                            </div>
                            <Progress
                              value={progress.completion_percentage || 0}
                              color={progress.status === 'completed' ? 'success' : 'warning'}
                              size="sm"
                            />
                          </CardBody>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </Tab>
              
              <Tab key="connections" title={`Friends (${socialConnections.length})`}>
                <div className="mt-4">
                  {socialConnections.length === 0 ? (
                    <div className="text-center py-8 text-default-600">
                      <i className="bi bi-people text-4xl mb-2 block"></i>
                      <p>No social connections</p>
                      <p className="text-sm">Connect with other users to build your network</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {socialConnections.map((connection) => {
                        const isUserInitiator = connection.user_id === currentUser.id;
                        const friend = isUserInitiator ? connection.ally : connection.user;
                        const friendName = friend?.user_metadata?.display_name || friend?.email;
                        
                        return (
                          <div key={connection.id} className="flex items-center gap-3 p-2 border rounded">
                            <Avatar
                              src={friend?.user_metadata?.avatar_url}
                              name={friendName}
                              size="sm"
                            />
                            <div className="flex-1 min-w-0">
                              <p className="font-medium truncate">{friendName}</p>
                              <p className="text-xs text-default-500">
                                Connected {new Date(connection.accepted_at).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </Tab>
            </Tabs>
          </CardBody>
        </Card>
      </div>

      {/* Data Summary */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Data Summary</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{skillsData.length}</div>
              <div className="text-sm text-default-600">Skills Tracked</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-success">{learningProgress.length}</div>
              <div className="text-sm text-default-600">Courses Started</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-warning">{socialConnections.length}</div>
              <div className="text-sm text-default-600">Friends Connected</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-secondary">{calculateProfileCompleteness()}%</div>
              <div className="text-sm text-default-600">Profile Complete</div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default ProfileSystemVerification;
