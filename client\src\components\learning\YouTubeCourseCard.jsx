import React, { useState } from 'react';
import { Card, CardBody, Button, Chip, Progress, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@heroui/react';
import { motion } from 'framer-motion';
import YouTubePlayer from './YouTubePlayer';

/**
 * YouTube Course Card Component
 * 
 * Displays YouTube videos in a card format within the learning system.
 * Features:
 * - Video thumbnail and metadata display
 * - Progress tracking visualization
 * - Modal video player integration
 * - Responsive design
 * - Action buttons for enrollment and viewing
 */
const YouTubeCourseCard = ({ 
  video, 
  progress = 0, 
  isEnrolled = false, 
  onEnroll, 
  onProgress, 
  onComplete,
  className = '' 
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);

  const handleEnroll = async () => {
    if (onEnroll) {
      setIsLoading(true);
      try {
        await onEnroll(video);
      } catch (error) {
        console.error('Error enrolling in video:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleWatchVideo = () => {
    onOpen();
  };

  const formatDuration = (minutes) => {
    if (!minutes) return 'Unknown';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getProgressColor = () => {
    if (progress >= 100) return 'success';
    if (progress >= 50) return 'warning';
    return 'primary';
  };

  const getStatusChip = () => {
    if (progress >= 100) {
      return (
        <Chip color="success" size="sm" variant="flat">
          ✓ Completed
        </Chip>
      );
    }
    if (progress > 0) {
      return (
        <Chip color="warning" size="sm" variant="flat">
          In Progress
        </Chip>
      );
    }
    if (isEnrolled) {
      return (
        <Chip color="primary" size="sm" variant="flat">
          Enrolled
        </Chip>
      );
    }
    return null;
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <Card className="youtube-course-card h-full hover:shadow-lg transition-all duration-300 group">
          <CardBody className="p-0">
            {/* Video Thumbnail */}
            <div className="relative overflow-hidden rounded-t-lg">
              <img
                src={video.thumbnail}
                alt={video.title}
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
              
              {/* Play Overlay */}
              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <Button
                  isIconOnly
                  color="primary"
                  variant="solid"
                  size="lg"
                  className="bg-white/20 backdrop-blur-sm"
                  onClick={handleWatchVideo}
                >
                  <i className="bi bi-play-fill text-2xl"></i>
                </Button>
              </div>

              {/* Duration Badge */}
              {video.duration && (
                <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {formatDuration(video.duration)}
                </div>
              )}

              {/* Provider Badge */}
              <div className="absolute top-2 left-2">
                <Chip color="danger" size="sm" variant="solid">
                  YouTube
                </Chip>
              </div>
            </div>

            {/* Video Information */}
            <div className="p-4 space-y-3">
              {/* Title and Channel */}
              <div>
                <h3 className="font-semibold text-lg line-clamp-2 mb-1 group-hover:text-primary transition-colors">
                  {video.title}
                </h3>
                <p className="text-sm text-default-600">
                  {video.channelTitle}
                </p>
              </div>

              {/* Description */}
              {video.description && (
                <p className="text-sm text-default-600 line-clamp-3">
                  {video.description}
                </p>
              )}

              {/* Progress Bar (if enrolled) */}
              {isEnrolled && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-default-600">Progress</span>
                    <span className="font-medium">{progress}%</span>
                  </div>
                  <Progress 
                    value={progress} 
                    color={getProgressColor()}
                    size="sm"
                  />
                </div>
              )}

              {/* Status and Metadata */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusChip()}
                  {video.publishedAt && (
                    <span className="text-xs text-default-500">
                      {new Date(video.publishedAt).toLocaleDateString()}
                    </span>
                  )}
                </div>
                
                {/* View Count (if available) */}
                {video.viewCount && (
                  <span className="text-xs text-default-500">
                    {video.viewCount.toLocaleString()} views
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                {!isEnrolled ? (
                  <Button
                    color="primary"
                    variant="solid"
                    size="sm"
                    className="flex-1"
                    isLoading={isLoading}
                    onClick={handleEnroll}
                  >
                    <i className="bi bi-plus-circle mr-1"></i>
                    Enroll
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="solid"
                    size="sm"
                    className="flex-1"
                    onClick={handleWatchVideo}
                  >
                    <i className="bi bi-play-circle mr-1"></i>
                    {progress > 0 ? 'Continue' : 'Start'}
                  </Button>
                )}
                
                <Button
                  color="default"
                  variant="flat"
                  size="sm"
                  isIconOnly
                  as="a"
                  href={video.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <i className="bi bi-box-arrow-up-right"></i>
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Video Player Modal */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="4xl"
        scrollBehavior="inside"
        classNames={{
          base: "youtube-player-modal",
          body: "p-0",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1 px-6 py-4">
            <h2 className="text-xl font-bold line-clamp-2">{video.title}</h2>
            <div className="flex items-center gap-2 text-sm text-default-600">
              <Chip color="danger" size="sm" variant="flat">
                YouTube
              </Chip>
              <span>{video.channelTitle}</span>
              {video.duration && (
                <span>• {formatDuration(video.duration)}</span>
              )}
            </div>
          </ModalHeader>
          
          <ModalBody className="px-0">
            <YouTubePlayer
              videoId={video.id}
              videoData={video}
              onProgress={onProgress}
              onComplete={onComplete}
              autoPlay={true}
              showControls={true}
            />
          </ModalBody>
          
          <ModalFooter className="px-6 py-4">
            <Button 
              color="danger" 
              variant="flat" 
              onPress={onClose}
            >
              Close
            </Button>
            <Button
              color="primary"
              variant="flat"
              as="a"
              href={video.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <i className="bi bi-box-arrow-up-right mr-1"></i>
              Open in YouTube
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default YouTubeCourseCard;
