/**
 * Agreement Generation Integration Tests
 * 
 * Tests the complete agreement generation workflow through the UI,
 * including project creation, wizard navigation, and agreement generation.
 */

import { test, expect } from '@playwright/test';

test.describe('Agreement Generation Integration', () => {
  test('should complete full project wizard and generate agreement', async ({ page }) => {
    console.log('🚀 Starting full agreement generation integration test...');

    // Navigate to the site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Take initial screenshot
    await page.screenshot({ path: 'test-results/01-initial-load.png', fullPage: true });

    // Check if we need to log in
    const isLoggedIn = await page.locator('[data-testid="user-profile"], .user-profile, [aria-label*="profile"], [aria-label*="Profile"]').isVisible();
    
    if (!isLoggedIn) {
      console.log('🔐 Logging in...');
      
      // Look for sign in button
      const signInButton = page.locator('button:has-text("Sign In"), a:has-text("Sign In"), [data-testid="sign-in"]').first();
      if (await signInButton.isVisible()) {
        await signInButton.click();
        await page.waitForLoadState('networkidle');
      }

      // Fill in credentials
      await page.fill('input[type="email"], input[name="email"], #email', '<EMAIL>');
      await page.fill('input[type="password"], input[name="password"], #password', 'TestPassword123!');
      
      // Submit login
      await page.click('button[type="submit"], button:has-text("Sign In"), button:has-text("Log In")');
      await page.waitForLoadState('networkidle');
      
      console.log('✅ Logged in successfully');
    } else {
      console.log('✅ Already logged in');
    }

    await page.screenshot({ path: 'test-results/02-after-login.png', fullPage: true });

    // Navigate to project creation
    console.log('📋 Navigating to project creation...');
    
    // Try multiple ways to access project creation
    const projectCreationSelectors = [
      'a[href*="/project/wizard"], a[href*="/project/create"]',
      'button:has-text("Create Project"), button:has-text("New Project")',
      'a:has-text("Create Project"), a:has-text("New Project")',
      '[data-testid="create-project"]'
    ];

    let projectCreationFound = false;
    for (const selector of projectCreationSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible()) {
        await element.click();
        await page.waitForLoadState('networkidle');
        projectCreationFound = true;
        console.log(`✅ Found project creation via: ${selector}`);
        break;
      }
    }

    if (!projectCreationFound) {
      // Try navigating directly to the wizard
      console.log('🔄 Trying direct navigation to project wizard...');
      await page.goto('https://royalty.technology/project/wizard');
      await page.waitForLoadState('networkidle');
    }

    await page.screenshot({ path: 'test-results/03-project-wizard.png', fullPage: true });

    // Check if we're in the project wizard
    const isInWizard = await page.locator('h1:has-text("Create Project"), h1:has-text("Project Wizard"), .wizard-step, [data-testid="wizard"]').isVisible();
    
    if (isInWizard) {
      console.log('✅ Successfully accessed project wizard');
      
      // Fill out project basics
      console.log('📝 Filling out project basics...');
      
      await page.fill('input[name="name"], input[placeholder*="project name"], #project-name', 'Test Agreement Project');
      await page.fill('textarea[name="description"], textarea[placeholder*="description"], #project-description', 'A test project for agreement generation testing');
      
      // Select project type
      const projectTypeSelector = page.locator('select[name="project_type"], select[name="type"], #project-type').first();
      if (await projectTypeSelector.isVisible()) {
        await projectTypeSelector.selectOption('SOFTWARE');
      }

      await page.screenshot({ path: 'test-results/04-project-basics-filled.png', fullPage: true });

      // Continue through wizard steps
      console.log('➡️ Proceeding through wizard steps...');
      
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue"), [data-testid="next-step"]').first();
      if (await nextButton.isVisible()) {
        await nextButton.click();
        await page.waitForLoadState('networkidle');
      }

      await page.screenshot({ path: 'test-results/05-wizard-step-2.png', fullPage: true });

      // Continue to agreement step (typically step 7)
      console.log('🔄 Navigating to agreement generation step...');
      
      // Try to find and click through to the agreement step
      for (let step = 2; step <= 7; step++) {
        const stepButton = page.locator(`button:has-text("Step ${step}"), .wizard-step:nth-child(${step}), [data-step="${step}"]`).first();
        if (await stepButton.isVisible()) {
          await stepButton.click();
          await page.waitForLoadState('networkidle');
        } else {
          // Try next button
          const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
          if (await nextBtn.isVisible()) {
            await nextBtn.click();
            await page.waitForLoadState('networkidle');
          }
        }
        
        // Check if we've reached the agreement step
        const agreementStep = await page.locator('h2:has-text("Agreement"), h3:has-text("Agreement"), .agreement-section').isVisible();
        if (agreementStep) {
          console.log(`✅ Reached agreement step at step ${step}`);
          break;
        }
      }

      await page.screenshot({ path: 'test-results/06-agreement-step.png', fullPage: true });

      // Test agreement generation
      console.log('🔧 Testing agreement generation...');
      
      const generateButton = page.locator('button:has-text("Generate Agreement"), button:has-text("Create Agreement"), [data-testid="generate-agreement"]').first();
      if (await generateButton.isVisible()) {
        await generateButton.click();
        await page.waitForTimeout(3000); // Wait for generation
        
        // Check for generated agreement content
        const agreementContent = await page.locator('.agreement-content, .generated-agreement, textarea[name="agreement"], #agreement-text').isVisible();
        
        if (agreementContent) {
          console.log('✅ Agreement generated successfully');
          
          // Verify agreement contains expected content
          const agreementText = await page.locator('.agreement-content, .generated-agreement, textarea[name="agreement"], #agreement-text').first().textContent();
          
          const hasProjectName = agreementText?.includes('Test Agreement Project');
          const hasLegalContent = agreementText?.includes('CONTRIBUTOR AGREEMENT') || agreementText?.includes('Revenue Tranche');
          
          console.log(`📋 Agreement validation:
            - Contains project name: ${hasProjectName}
            - Contains legal content: ${hasLegalContent}
            - Length: ${agreementText?.length || 0} characters`);
            
          expect(hasProjectName).toBe(true);
          expect(hasLegalContent).toBe(true);
          expect(agreementText?.length || 0).toBeGreaterThan(1000);
          
        } else {
          console.log('❌ Agreement content not found after generation');
        }
      } else {
        console.log('❌ Generate agreement button not found');
      }

      await page.screenshot({ path: 'test-results/07-final-agreement.png', fullPage: true });

    } else {
      console.log('❌ Could not access project wizard');
      
      // Log current page info for debugging
      const currentUrl = page.url();
      const pageTitle = await page.title();
      console.log(`Current URL: ${currentUrl}`);
      console.log(`Page title: ${pageTitle}`);
    }

    console.log('🎉 Agreement generation integration test completed');
  });
});
