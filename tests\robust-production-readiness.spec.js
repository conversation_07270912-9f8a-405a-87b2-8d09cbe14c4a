import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to find element with multiple selectors
async function findElementWithSelectors(page, selectors, timeout = 5000) {
  for (const selector of selectors) {
    try {
      const element = page.locator(selector);
      await element.waitFor({ timeout: timeout / selectors.length });
      if (await element.isVisible()) {
        return element;
      }
    } catch (e) {
      continue;
    }
  }
  return null;
}

// Helper function to click element with multiple selectors
async function clickElementWithSelectors(page, selectors, description = 'element') {
  const element = await findElementWithSelectors(page, selectors);
  if (element) {
    await element.click();
    console.log(`✅ Clicked ${description}`);
    return true;
  } else {
    console.log(`⚠️ Could not find ${description}`);
    return false;
  }
}

test.describe('Robust Production Readiness Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in with test credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify successful login
    await expect(page).toHaveURL(/dashboard/);
    console.log('✅ Successfully logged in');
  });

  test('Complete Studio-Project Creation Flow', async ({ page }) => {
    console.log('🚀 Testing studio-project creation flow...');

    // Navigate to project wizard
    await page.goto(`${PRODUCTION_URL}/project/wizard`);
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/project\/wizard/);
    console.log('✅ Project wizard loaded');

    // Test Studio Creation Interface
    console.log('🏢 Testing studio creation interface...');

    // Take screenshot to see what's actually on the page
    await page.screenshot({ path: 'test-results/project-wizard-initial-state.png', fullPage: true });
    console.log('📸 Screenshot saved: project-wizard-initial-state.png');

    // Wait for loading to complete and check for different possible states
    await page.waitForTimeout(2000); // Give time for studio loading

    // Check for loading state first
    const loadingElement = await findElementWithSelectors(page, [
      'text=Loading your studios...',
      '.animate-spin',
      'div:has-text("Loading")'
    ]);

    if (loadingElement) {
      console.log('⏳ Studios are loading, waiting...');
      await page.waitForTimeout(3000); // Wait for loading to complete
    }

    // Look for studio interface elements
    const studioSelectors = [
      'h1:has-text("Choose Your Studio")',
      'h2:has-text("Choose Your Studio")',
      'h3:has-text("Studio Selection")',
      'button:has-text("Create Studio")',
      'button:has-text("New Studio")',
      'button:has-text("Create")',
      'button:has-text("Create New Studio")',
      'input[type="radio"]',
      '[role="radio"]'
    ];

    // Also check for project basics (Step 1) in case studio is already selected
    const projectBasicsSelectors = [
      'h1:has-text("Project Basics")',
      'h2:has-text("Project Basics")',
      'input[placeholder*="project name" i]',
      'input[placeholder*="Project Name" i]',
      'label:has-text("Project Name")'
    ];

    const studioElement = await findElementWithSelectors(page, studioSelectors);
    const projectBasicsElement = await findElementWithSelectors(page, projectBasicsSelectors);
    if (studioElement) {
      console.log('✅ Studio interface detected');

      // Take screenshot to see current state
      await page.screenshot({ path: 'test-results/studio-interface-state.png', fullPage: true });
      console.log('📸 Screenshot saved: studio-interface-state.png');

      // Check if user has existing studios or needs to create one
      const existingStudioCard = await findElementWithSelectors(page, [
        '.studio-card',
        '[data-testid="studio-card"]',
        'div:has-text("Select this studio")',
        'button:has-text("Select")'
      ]);

      if (existingStudioCard) {
        console.log('✅ User has existing studios - selecting first one');
        await existingStudioCard.click();
        await page.waitForTimeout(1000);

        // Look for continue/next button after selection
        await clickElementWithSelectors(page, [
          'button:has-text("Continue with")',
          'button:has-text("Next")',
          'button:has-text("Continue")'
        ], 'continue button after studio selection');

      } else {
        console.log('🏢 No existing studios found - triggering creation');

        // Try to start studio creation
        const createStudioClicked = await clickElementWithSelectors(page, [
          'button:has-text("Create Studio")',
          'button:has-text("New Studio")',
          'button:has-text("Create")',
          'button:has-text("+ Create New Studio")'
        ], 'studio creation button');

        if (createStudioClicked) {
        await page.waitForTimeout(1000);
        await page.screenshot({ path: 'test-results/after-create-studio-click.png', fullPage: true });
        console.log('📸 Screenshot after create studio click');

        // Business Type Selection
        console.log('📋 Testing business type selection...');
        const businessTypeClicked = await clickElementWithSelectors(page, [
          'input[value="established_business"]',
          'label:has-text("Established Business")',
          'label:has-text("Business Entity")',
          '[role="radio"]:has-text("Established")',
          'input[type="radio"]:nth-child(2)'
        ], 'established business option');
        
        if (businessTypeClicked) {
          await page.waitForTimeout(500);
          
          // Click Next
          const nextClicked = await clickElementWithSelectors(page, [
            'button:has-text("Next")',
            'button:has-text("Continue")',
            'button:has-text("Proceed")',
            'button[type="submit"]'
          ], 'next button');
          
          if (nextClicked) {
            await page.waitForTimeout(1000);
            console.log('✅ Business type selection completed');
          }
        }
      }
    }

    // Test Basic Studio Information (Step 2 of wizard)
    console.log('📝 Testing basic studio information...');
    await page.screenshot({ path: 'test-results/before-studio-name-search.png', fullPage: true });
    console.log('📸 Screenshot before searching for studio name field');

    // Look for studio name input field (HeroUI Input component)
    const studioNameSelectors = [
      'input[placeholder*="John\'s Creative Studio" i]',
      'input[placeholder*="Creative Studio" i]',
      'input[placeholder*="Studio Name" i]',
      'label:has-text("Studio Name") + div input',
      'label:has-text("Studio/Business Name") + div input',
      '[data-slot="input"]',
      'input[type="text"]'
    ];

    const studioNameFilled = await fillFieldWithSelectors(page, studioNameSelectors, 'Test Creative Studio', 'studio name');

    if (studioNameFilled) {
      await page.waitForTimeout(500);

      // Try to select industry (HeroUI Select component)
      const industryClicked = await clickElementWithSelectors(page, [
        'button[aria-haspopup="listbox"]',
        '[data-slot="trigger"]',
        'button:has-text("Select your industry")',
        'div:has-text("Select your industry")'
      ], 'industry dropdown');

      if (industryClicked) {
        await page.waitForTimeout(500);
        // Select technology option
        await clickElementWithSelectors(page, [
          'li:has-text("Technology")',
          '[data-key="technology"]',
          'li:has-text("💻 Technology")'
        ], 'technology industry option');
      }

      await page.waitForTimeout(500);

      // Continue with next button
      await clickElementWithSelectors(page, [
        'button:has-text("Next")',
        'button:has-text("Continue")'
      ], 'next button after studio info');
    }
    } else if (projectBasicsElement) {
      console.log('✅ Project Basics detected - studio already selected');

      // Take screenshot to see project basics
      await page.screenshot({ path: 'test-results/project-basics-state.png', fullPage: true });
      console.log('📸 Screenshot saved: project-basics-state.png');

      // Test basic project creation functionality
      const projectNameFilled = await fillFieldWithSelectors(page, [
        'input[placeholder*="project name" i]',
        'input[placeholder*="Project Name" i]',
        'label:has-text("Project Name") + div input',
        'input[type="text"]:first-of-type'
      ], 'Test Project Name', 'project name');

      if (projectNameFilled) {
        console.log('✅ Project name filled successfully');
      }

    } else {
      console.log('⚠️ Neither studio interface nor project basics detected');

      // Take screenshot to see what's actually there
      await page.screenshot({ path: 'test-results/unknown-state.png', fullPage: true });
      console.log('📸 Screenshot saved: unknown-state.png');
    }

    console.log('🎯 Studio creation flow test completed');
  });

  test('Test Track Page Kanban Functionality', async ({ page }) => {
    console.log('📊 Testing Track page kanban functionality...');
    
    await page.goto(`${PRODUCTION_URL}/track`);
    await page.waitForLoadState('networkidle');
    
    // Look for kanban board elements
    const kanbanSelectors = [
      '.kanban-board',
      '[data-testid="kanban-board"]',
      '.kanban-column',
      '.task-card',
      'h1:has-text("Track")',
      'h2:has-text("Projects")',
      'button:has-text("Add Task")',
      'button:has-text("Create Task")'
    ];
    
    const kanbanElement = await findElementWithSelectors(page, kanbanSelectors);
    if (kanbanElement) {
      console.log('✅ Kanban board interface detected');
      
      // Try to create a task
      const taskCreated = await clickElementWithSelectors(page, [
        'button:has-text("Add Task")',
        'button:has-text("Create Task")',
        'button:has-text("New Task")',
        '[data-testid="add-task"]'
      ], 'add task button');
      
      if (taskCreated) {
        console.log('✅ Task creation interface accessible');
      }
    } else {
      console.log('⚠️ Kanban board not detected');
    }
  });

  test('Test Earn Page Gigwork System', async ({ page }) => {
    console.log('💼 Testing Earn page gigwork system...');
    
    await page.goto(`${PRODUCTION_URL}/earn`);
    await page.waitForLoadState('networkidle');
    
    // Look for gigwork elements
    const gigworkSelectors = [
      'h1:has-text("Earn")',
      'button:has-text("Browse Gigs")',
      'button:has-text("Create Request")',
      '.gigwork-board',
      '.gig-card',
      'h2:has-text("Gigwork")'
    ];
    
    const gigworkElement = await findElementWithSelectors(page, gigworkSelectors);
    if (gigworkElement) {
      console.log('✅ Gigwork system interface detected');
    } else {
      console.log('⚠️ Gigwork system not detected');
    }
  });
});

// Helper function to fill field with multiple selectors
async function fillFieldWithSelectors(page, selectors, value, description = 'field') {
  const element = await findElementWithSelectors(page, selectors);
  if (element) {
    await element.fill(value);
    console.log(`✅ Filled ${description} with: ${value}`);
    return true;
  } else {
    console.log(`⚠️ Could not find ${description}`);
    return false;
  }
}
