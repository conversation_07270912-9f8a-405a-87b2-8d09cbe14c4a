import { test, expect } from '@playwright/test';

/**
 * Studio Creation Test Suite
 * 
 * Tests the studio creation workflow on the Start page
 * Verifies that users can create studios which contain projects
 */

test.describe('Studio Creation Workflow Test', () => {
  test('Test Studio Creation Options on Start Page', async ({ page }) => {
    console.log('🧪 Starting Studio Creation Workflow Test...');

    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.goto('https://royalty.technology');
    
    // Wait for login form and fill credentials
    await page.waitForSelector('input[type="email"]', { timeout: 10000 });
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for successful login and navigation
    await page.waitForURL('**/dashboard', { timeout: 15000 });
    
    // Navigate to Start page
    console.log('📍 Navigating to Start page...');
    await page.goto('https://royalty.technology/start');
    await page.waitForLoadState('networkidle');
    
    // Check for studio creation options
    console.log('🔍 Looking for studio creation options...');
    
    // Look for any buttons or links related to studio creation
    const studioButtons = await page.locator('text=/studio|Studio|Create.*Studio|Studio.*Creation/i').count();
    console.log(`🏢 Studio creation buttons found: ${studioButtons}`);
    
    // Check each tab for studio creation options
    const tabs = await page.locator('[role="tab"]').count();
    console.log(`📑 Tabs found: ${tabs}`);
    
    for (let i = 0; i < tabs; i++) {
      const tab = page.locator('[role="tab"]').nth(i);
      const tabText = await tab.textContent();
      console.log(`  Tab ${i + 1}: "${tabText}"`);
      
      // Click on each tab to check content
      await tab.click();
      await page.waitForTimeout(500);
      
      // Look for studio-related content in this tab
      const studioContent = await page.locator('text=/studio|Studio|Create.*Studio|Studio.*Creation/i').count();
      if (studioContent > 0) {
        console.log(`    ✅ Studio content found in tab: ${tabText}`);
      } else {
        console.log(`    ❌ No studio content in tab: ${tabText}`);
      }
    }
    
    // Check if onboarding flow includes studio creation
    console.log('🔍 Checking onboarding flow for studio creation...');
    
    // Look for onboarding button
    const onboardingButton = page.locator('text=/Start Onboarding|I\'m New Here/i');
    const hasOnboarding = await onboardingButton.count() > 0;
    
    if (hasOnboarding) {
      console.log('📋 Onboarding flow found - testing studio creation path...');
      
      // Click onboarding button
      await onboardingButton.first().click();
      await page.waitForTimeout(2000);
      
      // Look for team/studio creation options in onboarding
      const teamOptions = await page.locator('text=/team|Team|studio|Studio|collaboration/i').count();
      console.log(`👥 Team/Studio options in onboarding: ${teamOptions}`);
      
      // Look for specific studio creation flow
      const studioFlow = await page.locator('text=/Create.*Studio|Studio.*Creation|team.*choice/i').count();
      console.log(`🏢 Studio creation flow in onboarding: ${studioFlow}`);
      
      // Cancel onboarding to return to start page
      const cancelButton = page.locator('text=/Cancel|Back|Skip/i');
      if (await cancelButton.count() > 0) {
        await cancelButton.first().click();
        await page.waitForTimeout(1000);
      }
    } else {
      console.log('❌ No onboarding flow found');
    }
    
    // Summary
    console.log('\n📊 STUDIO CREATION TEST SUMMARY:');
    console.log(`- Studio creation buttons on Start page: ${studioButtons}`);
    console.log(`- Onboarding flow available: ${hasOnboarding}`);
    console.log('- Test completed');
    
    // The test should identify if studio creation is missing from Start page
    if (studioButtons === 0) {
      console.log('⚠️ ISSUE IDENTIFIED: No direct studio creation option on Start page');
      console.log('💡 RECOMMENDATION: Add studio creation card/button to Start page');
    } else {
      console.log('✅ Studio creation options found on Start page');
    }
  });
});
