import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Task Creation Interface Test', () => {
  let page;
  let context;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'log' && msg.text().includes('[KanbanBoard]')) {
        console.log(`🔍 Console log: ${msg.text()}`);
      }
      if (msg.type() === 'error') {
        console.log(`🔍 Console error: ${msg.text()}`);
      }
    });
  });

  test.afterAll(async () => {
    await context.close();
  });

  test('Test Task Creation Interface on Track Page', async () => {
    console.log('\n🧪 Starting Task Creation Interface Test...');
    
    // Step 1: Login
    console.log('🔐 Logging in...');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
    
    if (await emailInput.isVisible() && await passwordInput.isVisible()) {
      await emailInput.fill(TEST_CREDENTIALS.email);
      await passwordInput.fill(TEST_CREDENTIALS.password);
      
      const submitButton = page.locator('button[type="submit"]').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
      }
    }
    
    // Step 2: Navigate to Track page
    console.log('📍 Navigating to Track page...');
    await page.goto(`${PRODUCTION_URL}/#/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Step 3: Look for Add Task buttons
    console.log('🔍 Looking for Add Task buttons...');
    const addTaskButtons = page.locator('button:has-text("Add Task"), button:has-text("Add"), button:has-text("Create Task")');
    const buttonCount = await addTaskButtons.count();
    console.log(`➕ Add Task buttons found: ${buttonCount}`);
    
    if (buttonCount > 0) {
      // List all buttons found
      for (let i = 0; i < buttonCount; i++) {
        const button = addTaskButtons.nth(i);
        const buttonText = await button.textContent();
        const isVisible = await button.isVisible();
        console.log(`  Button ${i + 1}: "${buttonText}" - Visible: ${isVisible}`);
      }
      
      // Step 4: Try to click the first visible Add Task button
      console.log('🖱️ Attempting to click Add Task button...');
      const firstVisibleButton = addTaskButtons.first();
      
      if (await firstVisibleButton.isVisible()) {
        await firstVisibleButton.click();
        await page.waitForTimeout(2000);
        
        // Step 5: Check if task creation modal opened
        console.log('📋 Checking for task creation modal...');
        const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
        const isModalVisible = await modal.isVisible();
        console.log(`📋 Task creation modal visible: ${isModalVisible}`);
        
        if (isModalVisible) {
          // Step 6: Check modal form fields
          console.log('📝 Checking modal form fields...');
          
          const titleInput = page.locator('input[placeholder*="title"], input[name="title"], input[id*="title"]');
          const isTitleVisible = await titleInput.isVisible();
          console.log(`  📝 Title input: ${isTitleVisible}`);
          
          const descriptionInput = page.locator('textarea[placeholder*="description"], textarea[name="description"]');
          const isDescriptionVisible = await descriptionInput.isVisible();
          console.log(`  📄 Description textarea: ${isDescriptionVisible}`);
          
          const prioritySelect = page.locator('select[name="priority"], [data-testid*="priority"]');
          const isPriorityVisible = await prioritySelect.isVisible();
          console.log(`  🏷️ Priority selector: ${isPriorityVisible}`);
          
          const statusSelect = page.locator('select[name="status"], [data-testid*="status"]');
          const isStatusVisible = await statusSelect.isVisible();
          console.log(`  📊 Status selector: ${isStatusVisible}`);
          
          const submitButton = page.locator('button[type="submit"]').first();
          const isSubmitVisible = await submitButton.isVisible();
          console.log(`  💾 Submit button: ${isSubmitVisible}`);
          
          // Step 7: Try to create a test task
          if (isTitleVisible) {
            console.log('🧪 Testing task creation...');
            
            await titleInput.fill('Test Task from Playwright');
            if (isDescriptionVisible) {
              await descriptionInput.fill('This is a test task created by the automated test suite.');
            }
            
            // Take screenshot of filled form
            await page.screenshot({ path: 'test-results/task-creation-form.png', fullPage: true });
            
            if (isSubmitVisible) {
              console.log('💾 Submitting task...');
              await submitButton.click();
              await page.waitForTimeout(3000);
              
              // Check if modal closed (indicating success)
              const isModalStillVisible = await modal.isVisible();
              console.log(`📋 Modal still visible after submit: ${isModalStillVisible}`);
              
              if (!isModalStillVisible) {
                console.log('✅ Task creation appears successful - modal closed');
                
                // Look for the new task in the kanban board
                console.log('🔍 Looking for new task in kanban board...');
                const taskCards = page.locator('.task-card, [data-testid*="task"], .kanban-task');
                const taskCount = await taskCards.count();
                console.log(`📝 Task cards found after creation: ${taskCount}`);
                
                // Look for our specific test task (handle multiple instances)
                const testTasks = page.locator('text=Test Task from Playwright');
                const testTaskCount = await testTasks.count();
                console.log(`🎯 Test tasks found in kanban: ${testTaskCount}`);

                if (testTaskCount > 0) {
                  console.log('🎉 SUCCESS: Task creation is fully functional!');
                } else {
                  console.log('⚠️ Task created but not visible in kanban board');
                }
              } else {
                console.log('⚠️ Modal still visible - possible validation error');
                
                // Check for error messages
                const errorMessages = page.locator('.error, .text-red, [role="alert"]');
                const errorCount = await errorMessages.count();
                if (errorCount > 0) {
                  for (let i = 0; i < errorCount; i++) {
                    const errorText = await errorMessages.nth(i).textContent();
                    console.log(`🚨 Error message: ${errorText}`);
                  }
                }
              }
            } else {
              console.log('❌ No submit button found');
            }
          } else {
            console.log('❌ No title input found');
          }
          
          // Step 8: Close modal if still open
          const closeButton = page.locator('button:has-text("Cancel"), button:has-text("Close"), [aria-label="Close"]');
          if (await modal.isVisible() && await closeButton.isVisible()) {
            await closeButton.click();
            console.log('❌ Closed task creation modal');
          }
          
        } else {
          console.log('❌ Task creation modal did not open');
        }
      } else {
        console.log('❌ Add Task button not visible');
      }
    } else {
      console.log('❌ No Add Task buttons found');
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/task-creation-final.png', fullPage: true });
    
    console.log('\n📊 TASK CREATION TEST SUMMARY:');
    console.log(`- Add Task buttons found: ${buttonCount}`);
    console.log('- Test completed');
  });
});
