import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Input, Button, Tabs, Tab } from '@heroui/react';
import { Search, FileText, ExternalLink, BookOpen } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const Documentation = () => {
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [documentation, setDocumentation] = useState([]);
  const [filteredDocs, setFilteredDocs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    loadDocumentation();
  }, []);

  useEffect(() => {
    filterDocumentation();
  }, [searchTerm, selectedCategory, documentation]);

  const loadDocumentation = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('learning_content')
        .select('*')
        .eq('content_type', 'documentation')
        .eq('is_active', true)
        .order('category', { ascending: true });

      if (error) throw error;

      setDocumentation(data || []);
    } catch (error) {
      console.error('Error loading documentation:', error);
      // Fallback to static documentation
      setDocumentation(getStaticDocumentation());
    } finally {
      setLoading(false);
    }
  };

  const getStaticDocumentation = () => [
    {
      id: 'getting-started',
      title: 'Getting Started Guide',
      description: 'Learn the basics of using the Royaltea platform',
      category: 'basics',
      content_url: '/docs/getting-started',
      external_url: null,
      tags: ['beginner', 'setup', 'basics']
    },
    {
      id: 'project-creation',
      title: 'Creating Your First Project',
      description: 'Step-by-step guide to setting up a new project',
      category: 'projects',
      content_url: '/docs/project-creation',
      external_url: null,
      tags: ['projects', 'setup', 'beginner']
    },
    {
      id: 'team-collaboration',
      title: 'Team Collaboration Features',
      description: 'How to work effectively with your team members',
      category: 'collaboration',
      content_url: '/docs/team-collaboration',
      external_url: null,
      tags: ['teams', 'collaboration', 'intermediate']
    },
    {
      id: 'revenue-sharing',
      title: 'Revenue Sharing System',
      description: 'Understanding how revenue distribution works',
      category: 'revenue',
      content_url: '/docs/revenue-sharing',
      external_url: null,
      tags: ['revenue', 'legal', 'advanced']
    },
    {
      id: 'api-reference',
      title: 'API Reference',
      description: 'Complete API documentation for developers',
      category: 'development',
      content_url: '/docs/api-reference',
      external_url: null,
      tags: ['api', 'development', 'advanced']
    }
  ];

  const filterDocumentation = () => {
    let filtered = documentation;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(doc => doc.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(term) ||
        doc.description.toLowerCase().includes(term) ||
        doc.tags?.some(tag => tag.toLowerCase().includes(term))
      );
    }

    setFilteredDocs(filtered);
  };

  const getCategories = () => {
    const categories = [...new Set(documentation.map(doc => doc.category))];
    return ['all', ...categories];
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'basics': return '🚀';
      case 'projects': return '📋';
      case 'collaboration': return '👥';
      case 'revenue': return '💰';
      case 'development': return '⚙️';
      default: return '📄';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Documentation</h2>
        <p className="text-white/70">Find guides, tutorials, and reference materials</p>
      </div>

      <div className="mb-6">
        <Input
          placeholder="Search documentation..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startContent={<Search size={16} />}
          className="max-w-md"
        />
      </div>

      <Tabs
        selectedKey={selectedCategory}
        onSelectionChange={setSelectedCategory}
        className="mb-6"
      >
        {getCategories().map(category => (
          <Tab
            key={category}
            title={
              <div className="flex items-center space-x-2">
                <span>{category === 'all' ? '📚' : getCategoryIcon(category)}</span>
                <span className="capitalize">{category === 'all' ? 'All' : category}</span>
              </div>
            }
          />
        ))}
      </Tabs>

      {filteredDocs.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <BookOpen size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Documentation Found</h3>
            <p className="text-gray-600">
              {searchTerm ? 'Try adjusting your search terms.' : 'No documentation available in this category.'}
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredDocs.map((doc) => (
            <Card key={doc.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">{getCategoryIcon(doc.category)}</div>
                    <div>
                      <h3 className="text-lg font-semibold">{doc.title}</h3>
                      <p className="text-gray-600 text-sm">{doc.description}</p>
                    </div>
                  </div>
                  {doc.external_url && (
                    <ExternalLink size={16} className="text-gray-400" />
                  )}
                </div>
              </CardHeader>
              <CardBody className="pt-2">
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {doc.tags?.map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    startContent={<FileText size={14} />}
                    as="a"
                    href={doc.external_url || doc.content_url}
                    target={doc.external_url ? "_blank" : "_self"}
                  >
                    Read More
                  </Button>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default Documentation;
