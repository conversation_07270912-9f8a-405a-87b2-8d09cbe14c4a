import { test, expect } from '@playwright/test';

test.describe('Studio Creation Styling Verification', () => {
  test('should have proper contrast and visibility for studio creation flow', async ({ page }) => {
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForTimeout(2000);
    
    // Navigate to project wizard
    await page.goto('https://royalty.technology/project/wizard');
    await page.waitForTimeout(2000);
    
    // Take a screenshot to verify styling improvements
    await page.screenshot({ 
      path: 'studio-creation-styling.png',
      fullPage: true 
    });
    
    // Verify main heading is visible with proper contrast
    const mainHeading = page.locator('h2:has-text("Choose Your Studio")');
    await expect(mainHeading).toBeVisible();
    
    // Verify the heading has proper text color (should be dark on light background)
    const headingColor = await mainHeading.evaluate(el => 
      window.getComputedStyle(el).color
    );
    console.log('Main heading color:', headingColor);
    
    // Verify description text is visible
    const description = page.locator('p:has-text("Studios help us generate legal agreements")');
    await expect(description).toBeVisible();
    
    // Verify the description has proper text color
    const descriptionColor = await description.evaluate(el => 
      window.getComputedStyle(el).color
    );
    console.log('Description color:', descriptionColor);
    
    // Check if we're in the studio creation flow (should see business type selection)
    const businessTypeSection = page.locator('text=Individual/Freelancer');
    if (await businessTypeSection.isVisible()) {
      console.log('✅ Studio creation flow is visible');
      
      // Verify radio button labels are readable
      const individualLabel = page.locator('text=Working as yourself, no formal business entity');
      await expect(individualLabel).toBeVisible();
      
      const businessLabel = page.locator('text=LLC, Corporation, Partnership, or other business entity');
      await expect(businessLabel).toBeVisible();
      
      // Check text colors for radio button descriptions
      const individualDesc = await individualLabel.evaluate(el => 
        window.getComputedStyle(el).color
      );
      console.log('Individual description color:', individualDesc);
      
      // Verify step indicator is visible
      const stepIndicator = page.locator('text=Step 1 of 5');
      await expect(stepIndicator).toBeVisible();
      
      console.log('✅ All text elements are visible with proper styling');
    } else {
      console.log('ℹ️ Not in studio creation flow - may be showing existing studios');
    }
    
    // Verify background is light (white/light gray)
    const bodyBg = await page.evaluate(() => 
      window.getComputedStyle(document.body).backgroundColor
    );
    console.log('Body background color:', bodyBg);
    
    console.log('✅ Studio creation styling verification completed');
  });
});
