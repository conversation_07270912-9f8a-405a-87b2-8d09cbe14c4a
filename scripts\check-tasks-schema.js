#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ'
);

async function checkTasksSchema() {
  console.log('🔍 Checking tasks table schema...');
  
  try {
    // Try to get a sample task to see what columns exist
    const { data: testData, error: testError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);
    
    if (testError) {
      console.log('❌ Tasks table error:', testError);
    } else {
      console.log('✅ Tasks table accessible');
      if (testData && testData.length > 0) {
        console.log('📋 Available columns:', Object.keys(testData[0]).sort());
      } else {
        console.log('📋 No tasks found, trying to insert a test task to see schema...');
        
        // Try to insert a minimal task to see what columns are required/available
        const { data: insertData, error: insertError } = await supabase
          .from('tasks')
          .insert({
            title: 'Schema Test Task',
            project_id: '99377e61-0dc9-4f9c-8471-e750f869cbe8'
          })
          .select()
          .single();
          
        if (insertError) {
          console.log('❌ Insert error (shows missing columns):', insertError);
        } else {
          console.log('✅ Insert successful, columns:', Object.keys(insertData).sort());
          
          // Clean up test task
          await supabase.from('tasks').delete().eq('id', insertData.id);
        }
      }
    }
  } catch (err) {
    console.log('❌ Error:', err.message);
  }
}

checkTasksSchema().catch(console.error);
