/**
 * Project Detail Debug Test
 * 
 * Tests to debug why the project detail page is showing blank content
 */

import { test, expect } from '@playwright/test';

test.describe('Project Detail Debug', () => {
  test('should debug project detail page loading', async ({ page }) => {
    console.log('🔍 Starting project detail debug test...');

    // Navigate to the site and login
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Check if we need to log in
    const isLoggedIn = await page.locator('[data-testid="user-profile"], .user-profile, [aria-label*="profile"], [aria-label*="Profile"]').isVisible();
    
    if (!isLoggedIn) {
      console.log('🔐 Logging in...');
      
      // Look for sign in button
      const signInButton = page.locator('button:has-text("Sign In"), a:has-text("Sign In"), [data-testid="sign-in"]').first();
      if (await signInButton.isVisible()) {
        await signInButton.click();
        await page.waitForLoadState('networkidle');
      }

      // Fill in credentials
      await page.fill('input[type="email"], input[name="email"], #email', '<EMAIL>');
      await page.fill('input[type="password"], input[name="password"], #password', 'TestPassword123!');
      
      // Submit login
      await page.click('button[type="submit"], button:has-text("Sign In"), button:has-text("Log In")');
      await page.waitForLoadState('networkidle');
      
      console.log('✅ Logged in successfully');
    }

    // Navigate to projects list first
    console.log('📋 Navigating to projects list...');
    await page.goto('https://royalty.technology/projects');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/debug-01-projects-list.png', fullPage: true });

    // Find the first project link
    const projectLinks = await page.locator('a[href*="/project/"]').all();
    console.log(`Found ${projectLinks.length} project links`);

    if (projectLinks.length > 0) {
      // Get the first project URL
      const firstProjectUrl = await projectLinks[0].getAttribute('href');
      console.log(`🔗 First project URL: ${firstProjectUrl}`);

      // Navigate to the project detail page
      await page.goto(`https://royalty.technology${firstProjectUrl}`);
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: 'test-results/debug-02-project-detail-initial.png', fullPage: true });

      // Check for loading indicators
      const loadingIndicator = await page.locator('.loading, [data-testid="loading"], .spinner').isVisible();
      console.log(`Loading indicator visible: ${loadingIndicator}`);

      // Check for error messages
      const errorMessage = await page.locator('.error, .alert-danger, [role="alert"]').isVisible();
      console.log(`Error message visible: ${errorMessage}`);

      // Check for project content
      const projectContainer = await page.locator('.project-detail-container').isVisible();
      console.log(`Project container visible: ${projectContainer}`);

      const projectHeader = await page.locator('.project-header').isVisible();
      console.log(`Project header visible: ${projectHeader}`);

      const projectTitle = await page.locator('.project-title, h1').isVisible();
      console.log(`Project title visible: ${projectTitle}`);

      // Get console logs
      const consoleLogs = [];
      page.on('console', msg => {
        consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      });

      // Wait a bit more and check again
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-results/debug-03-project-detail-after-wait.png', fullPage: true });

      // Check the actual HTML content
      const bodyContent = await page.locator('body').innerHTML();
      console.log('📄 Body content length:', bodyContent.length);

      // Check for specific elements
      const mainContent = await page.locator('main, #main-content').innerHTML();
      console.log('📄 Main content length:', mainContent.length);

      // Check if there are any React errors
      const reactErrors = await page.locator('[data-testid="error-boundary"], .error-boundary').isVisible();
      console.log(`React error boundary visible: ${reactErrors}`);

      // Check network requests
      const networkRequests = [];
      page.on('request', request => {
        networkRequests.push(`${request.method()} ${request.url()}`);
      });

      page.on('response', response => {
        if (!response.ok()) {
          console.log(`❌ Failed request: ${response.status()} ${response.url()}`);
        }
      });

      // Log all findings
      console.log('🔍 Debug Results:');
      console.log(`- Loading indicator: ${loadingIndicator}`);
      console.log(`- Error message: ${errorMessage}`);
      console.log(`- Project container: ${projectContainer}`);
      console.log(`- Project header: ${projectHeader}`);
      console.log(`- Project title: ${projectTitle}`);
      console.log(`- React errors: ${reactErrors}`);
      console.log(`- Body content length: ${bodyContent.length}`);
      console.log(`- Main content length: ${mainContent.length}`);

      // Check if the page is actually a different route
      const currentUrl = page.url();
      console.log(`Current URL: ${currentUrl}`);
      console.log(`Expected URL pattern: /project/[id]`);

      // Try to get the project ID from URL
      const urlMatch = currentUrl.match(/\/project\/([^\/]+)/);
      if (urlMatch) {
        const projectId = urlMatch[1];
        console.log(`Project ID from URL: ${projectId}`);

        // Check if we can access the project data directly
        const projectDataTest = await page.evaluate(async (id) => {
          try {
            // Try to access Supabase if available
            if (window.supabase) {
              const { data, error } = await window.supabase
                .from('projects')
                .select('*')
                .eq('id', id)
                .single();
              
              return { success: !error, data, error: error?.message };
            }
            return { success: false, error: 'Supabase not available' };
          } catch (err) {
            return { success: false, error: err.message };
          }
        }, projectId);

        console.log('📊 Project data test:', projectDataTest);
      }

      // Final screenshot
      await page.screenshot({ path: 'test-results/debug-04-project-detail-final.png', fullPage: true });

    } else {
      console.log('❌ No project links found in projects list');
      
      // Try to create a test project or navigate to a known project ID
      console.log('🔄 Trying to navigate to a test project directly...');
      await page.goto('https://royalty.technology/project/test-project-id');
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: 'test-results/debug-05-direct-project-navigation.png', fullPage: true });
    }

    console.log('🎉 Project detail debug test completed');
  });
});
