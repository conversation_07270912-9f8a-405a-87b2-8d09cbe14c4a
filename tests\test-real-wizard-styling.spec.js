import { test, expect } from '@playwright/test';

test.describe('Real Project Wizard Styling Tests', () => {
  test('Test Actual Project Creation Wizard Styling', async ({ page }) => {
    console.log('🚀 Testing the REAL project creation wizard styling...');
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Navigate directly to project creation wizard using the correct route
    console.log('📝 Navigating directly to project wizard...');
    await page.goto('https://royalty.technology/#/project/create');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take screenshot of the actual wizard
    await page.screenshot({ 
      path: 'test-results/real-project-wizard.png',
      fullPage: true 
    });
    
    // Check what's actually on the page now
    const pageContent = await page.textContent('body');
    console.log(`📄 Page content length: ${pageContent.length}`);
    
    // Look for wizard-specific content
    const hasProjectBasics = pageContent.includes('Project Basics');
    const hasCompanyInfo = pageContent.includes('Company Information');
    const hasProjectName = pageContent.includes('Project Name') || pageContent.includes('project name');
    const hasWizardSteps = pageContent.includes('Step') || pageContent.includes('step');
    
    console.log(`🧙 Has "Project Basics": ${hasProjectBasics}`);
    console.log(`🏢 Has "Company Information": ${hasCompanyInfo}`);
    console.log(`📝 Has "Project Name": ${hasProjectName}`);
    console.log(`📋 Has wizard steps: ${hasWizardSteps}`);
    
    // Check for duplicate labels - this was the main issue
    const projectBasicsCount = (pageContent.match(/Project Basics/g) || []).length;
    console.log(`📊 "Project Basics" appears ${projectBasicsCount} times`);
    
    if (projectBasicsCount > 2) {
      console.log('❌ ISSUE: Too many "Project Basics" labels found!');
    } else {
      console.log('✅ "Project Basics" duplication looks good');
    }
    
    // Check for form elements
    const formInputs = page.locator('input, select, textarea');
    const inputCount = await formInputs.count();
    console.log(`📝 Form elements found: ${inputCount}`);
    
    // Check for debug elements (should be 0)
    const debugElements = page.locator('[style*="background-color: red"], [style*="backgroundColor: red"]');
    const debugCount = await debugElements.count();
    console.log(`🐛 Debug elements found: ${debugCount}`);
    
    if (debugCount > 0) {
      console.log('❌ ISSUE: Debug elements still present!');
    } else {
      console.log('✅ No debug elements found');
    }
    
    // Check for proper styling classes
    const styledElements = page.locator('[class*="space-"], [class*="gap-"], [class*="grid"], [class*="flex"], [class*="rounded"], [class*="border"]');
    const styledCount = await styledElements.count();
    console.log(`🎨 Elements with proper styling: ${styledCount}`);
    
    // Check for specific styling improvements
    const companySection = page.locator('text="Company Information"');
    const companySectionVisible = await companySection.isVisible();
    console.log(`🏢 Company Information section visible: ${companySectionVisible}`);
    
    const thumbnailSection = page.locator('text="Project Thumbnail"');
    const thumbnailVisible = await thumbnailSection.isVisible();
    console.log(`🖼️ Project Thumbnail section visible: ${thumbnailVisible}`);
    
    const privacySection = page.locator('text="Project Privacy"');
    const privacyVisible = await privacySection.isVisible();
    console.log(`🔒 Project Privacy section visible: ${privacyVisible}`);
    
    // Test form interaction if wizard is loaded
    if (inputCount > 0) {
      console.log('🔧 Testing form interactions...');
      
      // Try to fill project name
      const projectNameInput = page.locator('input[placeholder*="project name"], input[placeholder*="Project name"]').first();
      if (await projectNameInput.isVisible()) {
        await projectNameInput.fill('Test Styling Project');
        console.log('📝 Successfully filled project name');
      }
      
      // Try to fill company name
      const companyNameInput = page.locator('input[placeholder*="company name"], input[placeholder*="Company name"]').first();
      if (await companyNameInput.isVisible()) {
        await companyNameInput.fill('Test Company Inc.');
        console.log('🏢 Successfully filled company name');
      }
      
      // Take screenshot after filling forms
      await page.screenshot({ 
        path: 'test-results/wizard-with-data.png',
        fullPage: true 
      });
    }
    
    // Check visual hierarchy
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    const headingCount = await headings.count();
    console.log(`📝 Headings found: ${headingCount}`);
    
    // Check for proper spacing
    const spacedElements = page.locator('[class*="space-y"], [class*="gap-"], [class*="mb-"], [class*="mt-"], [class*="p-"]');
    const spacedCount = await spacedElements.count();
    console.log(`📏 Elements with proper spacing: ${spacedCount}`);
    
    // Summary
    console.log('\n📋 STYLING ASSESSMENT SUMMARY:');
    console.log(`   Duplicate Labels: ${projectBasicsCount > 2 ? '❌ NEEDS FIX' : '✅ GOOD'}`);
    console.log(`   Debug Elements: ${debugCount > 0 ? '❌ NEEDS FIX' : '✅ GOOD'}`);
    console.log(`   Form Elements: ${inputCount > 0 ? '✅ PRESENT' : '❌ MISSING'}`);
    console.log(`   Styled Elements: ${styledCount > 0 ? '✅ PRESENT' : '❌ MISSING'}`);
    console.log(`   Visual Hierarchy: ${headingCount > 0 ? '✅ PRESENT' : '❌ MISSING'}`);
    
    console.log('✅ Real wizard styling test completed!');
  });
});
