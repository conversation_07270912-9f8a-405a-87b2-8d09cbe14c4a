import { test, expect } from '@playwright/test';

test.describe('<PERSON> Styling Fixes Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Studio Creation Wizard - Styling Improvements', async ({ page }) => {
    console.log('🎨 Testing Studio Creation Wizard Styling...');
    
    // Navigate to studio creation
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for visual verification
    await page.screenshot({ path: 'test-results/studio-creation-styling.png', fullPage: true });
    
    // Verify new styling classes are applied
    const studioWizard = page.locator('.studio-creation-wizard');
    await expect(studioWizard).toBeVisible();
    
    const studioCard = page.locator('.studio-creation-card');
    await expect(studioCard).toBeVisible();
    
    const studioHeader = page.locator('.studio-creation-header');
    await expect(studioHeader).toBeVisible();
    
    // Verify responsive text classes
    const responsiveHeading = page.locator('.wizard-heading-responsive');
    await expect(responsiveHeading).toBeVisible();
    
    const responsiveText = page.locator('.wizard-text-responsive');
    await expect(responsiveText).toBeVisible();
    
    // Verify section styling
    const sections = page.locator('.studio-creation-section');
    const sectionCount = await sections.count();
    console.log(`📋 Found ${sectionCount} studio creation sections`);
    expect(sectionCount).toBeGreaterThan(0);
    
    // Verify section headers
    const sectionHeaders = page.locator('.studio-creation-section-header');
    const headerCount = await sectionHeaders.count();
    console.log(`📝 Found ${headerCount} section headers`);
    expect(headerCount).toBeGreaterThan(0);
    
    // Verify form fields have proper styling
    const formFields = page.locator('.wizard-form-field');
    const fieldCount = await formFields.count();
    console.log(`📝 Found ${fieldCount} form fields with wizard styling`);
    expect(fieldCount).toBeGreaterThan(0);
    
    // Test form interaction
    const nameInput = page.locator('input[placeholder="Enter your studio name"]').first();
    await expect(nameInput).toBeVisible();
    await nameInput.fill('Test Studio Styling');
    
    const industryInput = page.locator('input[placeholder*="Software Development"]').first();
    await expect(industryInput).toBeVisible();
    await industryInput.fill('Software Development');
    
    // Verify action buttons styling
    const actionButtons = page.locator('.studio-creation-actions');
    await expect(actionButtons).toBeVisible();
    
    console.log('✅ Studio Creation Wizard styling test completed!');
  });

  test('Project Creation Wizard - Styling Improvements', async ({ page }) => {
    console.log('🎨 Testing Project Creation Wizard Styling...');
    
    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for visual verification
    await page.screenshot({ path: 'test-results/project-creation-styling.png', fullPage: true });
    
    // Verify wizard step content
    const wizardContent = page.locator('.wizard-step-content');
    await expect(wizardContent).toBeVisible();
    
    // Verify responsive heading
    const responsiveHeading = page.locator('.wizard-heading-responsive');
    await expect(responsiveHeading).toBeVisible();
    
    // Verify wizard cards
    const wizardCards = page.locator('.wizard-card');
    const cardCount = await wizardCards.count();
    console.log(`🃏 Found ${cardCount} wizard cards`);
    expect(cardCount).toBeGreaterThan(0);
    
    // Verify wizard sections
    const wizardSections = page.locator('.wizard-section');
    const sectionCount = await wizardSections.count();
    console.log(`📋 Found ${sectionCount} wizard sections`);
    expect(sectionCount).toBeGreaterThan(0);
    
    // Verify form fields
    const formFields = page.locator('.wizard-form-field');
    const fieldCount = await formFields.count();
    console.log(`📝 Found ${fieldCount} wizard form fields`);
    expect(fieldCount).toBeGreaterThan(0);
    
    // Verify grid layouts
    const wizardGrids = page.locator('.wizard-grid');
    const gridCount = await wizardGrids.count();
    console.log(`🔲 Found ${gridCount} wizard grids`);
    expect(gridCount).toBeGreaterThan(0);
    
    // Test form interaction
    const projectNameInput = page.locator('input[placeholder*="project name"], input[placeholder*="Project name"]').first();
    if (await projectNameInput.isVisible()) {
      await projectNameInput.fill('Test Project Styling');
      console.log('📝 Successfully filled project name');
    }
    
    // Verify spacing utilities
    const spacedElements = page.locator('.wizard-space-y');
    const spacedCount = await spacedElements.count();
    console.log(`📏 Found ${spacedCount} elements with wizard spacing`);
    expect(spacedCount).toBeGreaterThan(0);
    
    console.log('✅ Project Creation Wizard styling test completed!');
  });

  test('General Site Styling Improvements', async ({ page }) => {
    console.log('🎨 Testing General Site Styling...');
    
    // Navigate to dashboard
    await page.goto('https://royalty.technology/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for visual verification
    await page.screenshot({ path: 'test-results/general-styling.png', fullPage: true });
    
    // Verify responsive containers
    const responsiveContainers = page.locator('.responsive-container');
    const containerCount = await responsiveContainers.count();
    console.log(`📦 Found ${containerCount} responsive containers`);
    
    // Verify enhanced cards
    const enhancedCards = page.locator('.card-enhanced');
    const enhancedCardCount = await enhancedCards.count();
    console.log(`🃏 Found ${enhancedCardCount} enhanced cards`);
    
    // Verify form sections
    const formSections = page.locator('.form-section');
    const formSectionCount = await formSections.count();
    console.log(`📋 Found ${formSectionCount} form sections`);
    
    // Verify button groups
    const buttonGroups = page.locator('.button-group');
    const buttonGroupCount = await buttonGroups.count();
    console.log(`🔘 Found ${buttonGroupCount} button groups`);
    
    // Test responsive design
    const viewportSize = await page.viewportSize();
    console.log(`📱 Current viewport: ${viewportSize.width}x${viewportSize.height}`);
    
    // Check for overflow issues
    const overflowIssues = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      let count = 0;
      elements.forEach(el => {
        const rect = el.getBoundingClientRect();
        if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
          count++;
        }
      });
      return count;
    });
    
    console.log(`📏 Elements extending beyond viewport: ${overflowIssues}`);
    expect(overflowIssues).toBeLessThan(10); // Allow some tolerance
    
    // Check for proper spacing
    const spacedElements = page.locator('[class*="space-"], [class*="gap-"], [class*="mb-"], [class*="mt-"], [class*="p-"]');
    const spacedCount = await spacedElements.count();
    console.log(`📏 Elements with proper spacing: ${spacedCount}`);
    expect(spacedCount).toBeGreaterThan(0);
    
    // Check for proper grid layouts
    const gridElements = page.locator('[class*="grid"], [class*="flex"]');
    const gridCount = await gridElements.count();
    console.log(`🔲 Grid/flex elements: ${gridCount}`);
    expect(gridCount).toBeGreaterThan(0);
    
    console.log('✅ General site styling test completed!');
  });

  test('Responsive Design Verification', async ({ page }) => {
    console.log('📱 Testing Responsive Design...');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/mobile-studio-creation.png', fullPage: true });
    
    // Verify mobile-specific styling
    const mobileElements = page.locator('[class*="mobile"], [class*="sm:"], [class*="md:"]');
    const mobileCount = await mobileElements.count();
    console.log(`📱 Mobile-responsive elements: ${mobileCount}`);
    expect(mobileCount).toBeGreaterThan(0);
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.screenshot({ path: 'test-results/tablet-studio-creation.png', fullPage: true });
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.screenshot({ path: 'test-results/desktop-studio-creation.png', fullPage: true });
    
    console.log('✅ Responsive design test completed!');
  });

  test('Accessibility and Focus Management', async ({ page }) => {
    console.log('♿ Testing Accessibility and Focus Management...');
    
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Check for focus ring styles
    const focusElements = page.locator('.focus-ring, [class*="focus"]');
    const focusCount = await focusElements.count();
    console.log(`🎯 Focus-managed elements: ${focusCount}`);
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.waitForTimeout(500);
    
    // Check for screen reader elements
    const srElements = page.locator('.sr-only, [class*="sr-"]');
    const srCount = await srElements.count();
    console.log(`🔊 Screen reader elements: ${srCount}`);
    
    // Check for proper heading hierarchy
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    const headingCount = await headings.count();
    console.log(`📝 Headings found: ${headingCount}`);
    expect(headingCount).toBeGreaterThan(0);
    
    // Check for proper form labels
    const formLabels = page.locator('label, [aria-label], [aria-labelledby]');
    const labelCount = await formLabels.count();
    console.log(`🏷️ Form labels: ${labelCount}`);
    expect(labelCount).toBeGreaterThan(0);
    
    console.log('✅ Accessibility test completed!');
  });

  test('Loading States and Animations', async ({ page }) => {
    console.log('⏳ Testing Loading States and Animations...');
    
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Check for loading classes
    const loadingElements = page.locator('.wizard-loading, .loading-overlay, [class*="loading"]');
    const loadingCount = await loadingElements.count();
    console.log(`⏳ Loading state elements: ${loadingCount}`);
    
    // Check for animation classes
    const animationElements = page.locator('.wizard-fade-in, .wizard-slide-in, [class*="animate"]');
    const animationCount = await animationElements.count();
    console.log(`🎬 Animation elements: ${animationCount}`);
    
    // Test form submission loading state
    const nameInput = page.locator('input[placeholder="Enter your studio name"]').first();
    await nameInput.fill('Test Loading Studio');
    
    const industryInput = page.locator('input[placeholder*="Software Development"]').first();
    await industryInput.fill('Software Development');
    
    // Try to submit and check for loading state
    const submitButton = page.locator('button:has-text("Create Studio")').first();
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Wait for potential loading state
      await page.waitForTimeout(1000);
      
      // Check if loading state appears
      const loadingState = page.locator('[class*="loading"], [class*="spinner"], [class*="animate-spin"]');
      const hasLoading = await loadingState.count() > 0;
      console.log(`⏳ Loading state during submission: ${hasLoading}`);
    }
    
    console.log('✅ Loading states test completed!');
  });

  test('Error Handling and Validation Styling', async ({ page }) => {
    console.log('⚠️ Testing Error Handling and Validation Styling...');
    
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Check for error styling classes
    const errorElements = page.locator('.wizard-form-error, [class*="error"], [class*="invalid"]');
    const errorCount = await errorElements.count();
    console.log(`⚠️ Error styling elements: ${errorCount}`);
    
    // Check for success styling classes
    const successElements = page.locator('.wizard-form-success, [class*="success"], [class*="valid"]');
    const successCount = await successElements.count();
    console.log(`✅ Success styling elements: ${successCount}`);
    
    // Test form validation by submitting empty form
    const submitButton = page.locator('button:has-text("Create Studio")').first();
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Wait for validation errors
      await page.waitForTimeout(1000);
      
      // Check for validation error messages
      const validationErrors = page.locator('[class*="error"], [class*="invalid"], [role="alert"]');
      const validationCount = await validationErrors.count();
      console.log(`⚠️ Validation errors: ${validationCount}`);
      expect(validationCount).toBeGreaterThan(0);
    }
    
    console.log('✅ Error handling test completed!');
  });

  test('Dark Mode and Theme Support', async ({ page }) => {
    console.log('🌙 Testing Dark Mode and Theme Support...');
    
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Check for dark mode classes
    const darkModeElements = page.locator('[class*="dark"], [data-theme="dark"]');
    const darkModeCount = await darkModeElements.count();
    console.log(`🌙 Dark mode elements: ${darkModeCount}`);
    
    // Check for theme-aware styling
    const themeElements = page.locator('[class*="hsl(var(--"], [style*="hsl(var(--"]');
    const themeCount = await themeElements.count();
    console.log(`🎨 Theme-aware elements: ${themeCount}`);
    expect(themeCount).toBeGreaterThan(0);
    
    // Check for high contrast support
    const highContrastElements = page.locator('[class*="contrast"], [class*="high-contrast"]');
    const contrastCount = await highContrastElements.count();
    console.log(`🔍 High contrast elements: ${contrastCount}`);
    
    console.log('✅ Dark mode and theme test completed!');
  });

  test('Performance and Animation Optimization', async ({ page }) => {
    console.log('⚡ Testing Performance and Animation Optimization...');
    
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Check for reduced motion support
    const reducedMotionElements = page.locator('[class*="reduced-motion"], [class*="prefers-reduced-motion"]');
    const reducedMotionCount = await reducedMotionElements.count();
    console.log(`🎬 Reduced motion elements: ${reducedMotionCount}`);
    
    // Check for optimized animations
    const optimizedAnimations = page.locator('[class*="transition"], [class*="animate"], [style*="transition"]');
    const animationCount = await optimizedAnimations.count();
    console.log(`🎭 Optimized animations: ${animationCount}`);
    
    // Test page load performance
    const startTime = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`⏱️ Page load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds
    
    console.log('✅ Performance test completed!');
  });
}); 