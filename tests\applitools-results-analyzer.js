import { Eyes, Target, Configuration, BatchInfo } from '@applitools/eyes-playwright';

/**
 * Applitools Results Analyzer
 * 
 * This script analyzes Applitools test results and provides detailed feedback
 * about what the AI visual testing found, including any failures or issues.
 */

const APPLITOOLS_API_KEY = process.env.APPLITOOLS_API_KEY || 'N98LseXWBh4rhN7ku0fHh2RkXNiyosMpms0o0o0f2zp8110';

class ApplitoolsResultsAnalyzer {
  constructor() {
    this.results = [];
  }

  // Analyze test results from the Eyes SDK
  analyzeResults(testResults) {
    console.log('📊 APPLITOOLS VISUAL AI TEST RESULTS ANALYSIS');
    console.log('=' .repeat(60));
    
    if (!testResults) {
      console.error('❌ No test results provided');
      return;
    }

    // Extract key information
    const {
      name,
      status,
      isNew,
      isDifferent,
      isAborted,
      steps,
      matches,
      mismatches,
      missing,
      new: newSteps,
      appUrls,
      startedAt,
      duration,
      hostOS,
      hostApp,
      batchName
    } = testResults._result || testResults;

    console.log(`🧪 Test Name: ${name}`);
    console.log(`📱 Environment: ${hostApp} on ${hostOS}`);
    console.log(`⏱️ Duration: ${duration} seconds`);
    console.log(`📅 Started: ${startedAt}`);
    console.log(`🎯 Batch: ${batchName}`);
    console.log('');

    // Analyze test status
    console.log('📋 TEST STATUS ANALYSIS:');
    console.log(`   Status: ${status}`);
    console.log(`   Is New Baseline: ${isNew ? '✅ Yes' : '❌ No'}`);
    console.log(`   Has Differences: ${isDifferent ? '⚠️ Yes' : '✅ No'}`);
    console.log(`   Was Aborted: ${isAborted ? '❌ Yes' : '✅ No'}`);
    console.log('');

    // Analyze visual validation results
    console.log('👁️ VISUAL VALIDATION RESULTS:');
    console.log(`   Total Steps: ${steps}`);
    console.log(`   Matches: ${matches} ✅`);
    console.log(`   Mismatches: ${mismatches} ${mismatches > 0 ? '❌' : '✅'}`);
    console.log(`   Missing: ${missing} ${missing > 0 ? '⚠️' : '✅'}`);
    console.log(`   New: ${newSteps} ${newSteps > 0 ? '🆕' : '✅'}`);
    console.log('');

    // Provide dashboard links
    if (appUrls) {
      console.log('🔗 DETAILED RESULTS:');
      console.log(`   Session: ${appUrls.session}`);
      console.log(`   Batch: ${appUrls.batch}`);
      console.log('');
    }

    // Determine overall test health
    this.assessTestHealth(testResults);
    
    return {
      passed: status === 'Passed' && !isAborted,
      hasVisualIssues: mismatches > 0 || isDifferent,
      isBaseline: isNew,
      summary: this.generateSummary(testResults)
    };
  }

  assessTestHealth(testResults) {
    const { status, isAborted, mismatches, isDifferent } = testResults._result || testResults;
    
    console.log('🏥 TEST HEALTH ASSESSMENT:');
    
    if (isAborted) {
      console.log('   ❌ CRITICAL: Test was aborted - likely login or navigation failure');
      console.log('   🔧 Action Required: Fix login validation and navigation flow');
    } else if (status === 'Failed') {
      console.log('   ❌ FAILED: Test failed - check for functional issues');
      console.log('   🔧 Action Required: Review test logs and fix underlying issues');
    } else if (mismatches > 0 || isDifferent) {
      console.log('   ⚠️ VISUAL DIFFERENCES: AI detected visual changes');
      console.log('   🔧 Action Required: Review visual differences in dashboard');
    } else if (status === 'Passed') {
      console.log('   ✅ HEALTHY: Test passed with no visual issues detected');
      console.log('   🎉 Great job! Visual quality is maintained');
    }
    
    console.log('');
  }

  generateSummary(testResults) {
    const { name, status, isNew, mismatches, steps } = testResults._result || testResults;
    
    if (isNew) {
      return `🆕 ${name}: Created new visual baseline with ${steps} checkpoints`;
    } else if (status === 'Passed' && mismatches === 0) {
      return `✅ ${name}: All ${steps} visual checkpoints passed`;
    } else if (mismatches > 0) {
      return `⚠️ ${name}: ${mismatches} visual differences detected in ${steps} checkpoints`;
    } else {
      return `❌ ${name}: Test failed or was aborted`;
    }
  }

  // Generate production readiness report
  generateProductionReadinessReport(allResults) {
    console.log('🚀 PRODUCTION READINESS ASSESSMENT');
    console.log('=' .repeat(60));
    
    const totalTests = allResults.length;
    const passedTests = allResults.filter(r => r.passed).length;
    const visualIssues = allResults.filter(r => r.hasVisualIssues).length;
    const baselines = allResults.filter(r => r.isBaseline).length;
    
    console.log(`📊 Test Summary: ${passedTests}/${totalTests} tests passed`);
    console.log(`👁️ Visual Issues: ${visualIssues} tests with visual differences`);
    console.log(`🆕 New Baselines: ${baselines} baseline tests created`);
    console.log('');
    
    // Overall assessment
    const readinessScore = (passedTests / totalTests) * 100;
    console.log('🎯 PRODUCTION READINESS SCORE:');
    
    if (readinessScore >= 95) {
      console.log(`   ✅ EXCELLENT (${readinessScore.toFixed(1)}%) - Ready for production!`);
    } else if (readinessScore >= 80) {
      console.log(`   ⚠️ GOOD (${readinessScore.toFixed(1)}%) - Minor issues to address`);
    } else if (readinessScore >= 60) {
      console.log(`   🔧 NEEDS WORK (${readinessScore.toFixed(1)}%) - Several issues to fix`);
    } else {
      console.log(`   ❌ NOT READY (${readinessScore.toFixed(1)}%) - Major issues present`);
    }
    
    console.log('');
    
    // Detailed recommendations
    console.log('📋 RECOMMENDATIONS:');
    if (visualIssues > 0) {
      console.log('   🔍 Review visual differences in Applitools dashboard');
      console.log('   🎨 Fix styling and layout issues identified by AI');
    }
    if (passedTests < totalTests) {
      console.log('   🔧 Fix failing tests - likely login or navigation issues');
      console.log('   ✅ Ensure all tests can successfully authenticate and navigate');
    }
    if (baselines === totalTests) {
      console.log('   🔄 Run tests again to validate against baselines');
      console.log('   📈 Monitor for regressions in future test runs');
    }
    
    return {
      score: readinessScore,
      ready: readinessScore >= 95,
      issues: totalTests - passedTests,
      visualIssues: visualIssues
    };
  }
}

export { ApplitoolsResultsAnalyzer };
