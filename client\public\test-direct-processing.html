<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Comprehensive Template Processing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Direct Comprehensive Template Processing Test</h1>
    <p>This test verifies that the new direct template processing system is working correctly.</p>
    
    <div class="test-section info">
        <h3>Test Status</h3>
        <div id="test-status">Ready to test...</div>
    </div>
    
    <button onclick="runDirectProcessingTest()">🚀 Test Direct Processing</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    
    <div id="results"></div>

    <script>
        async function runDirectProcessingTest() {
            const resultsDiv = document.getElementById('results');
            const statusDiv = document.getElementById('test-status');
            
            statusDiv.innerHTML = '🔄 Running direct processing test...';
            resultsDiv.innerHTML = '';
            
            try {
                // Test 1: Load comprehensive template
                console.log('🧪 Test 1: Loading comprehensive template...');
                const response = await fetch('/templates/v2/comprehensive_contributor_agreement_template.md');
                
                if (!response.ok) {
                    throw new Error(`Template fetch failed: ${response.status}`);
                }
                
                const templateText = await response.text();
                console.log('✅ Template loaded:', templateText.length, 'characters');
                
                if (templateText.length < 50000) {
                    throw new Error(`Template too short: ${templateText.length} characters`);
                }
                
                addResult('✅ Template Loading', `Successfully loaded ${templateText.length} characters`, 'success');
                
                // Test 2: Simulate direct variable replacement
                console.log('🧪 Test 2: Testing direct variable replacement...');
                
                const mockProject = {
                    name: 'Test Project',
                    description: 'A test project for agreement generation',
                    type: 'software'
                };
                
                const mockContributor = {
                    name: 'John Doe',
                    email: '<EMAIL>'
                };
                
                const currentDate = new Date().toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                
                const variables = {
                    COMPANY_NAME: 'Test Company Inc.',
                    COMPANY_LEGAL_NAME: 'Test Company Inc. LLC',
                    COMPANY_STATE: 'Florida',
                    COMPANY_ADDRESS: '123 Business St, City, FL 12345',
                    CONTRIBUTOR_NAME: mockContributor.name,
                    CONTRIBUTOR_EMAIL: mockContributor.email,
                    PROJECT_NAME: mockProject.name,
                    PROJECT_DESCRIPTION: mockProject.description,
                    PROJECT_TYPE: 'Software',
                    EFFECTIVE_DATE: currentDate,
                    CURRENT_DATE: currentDate,
                    PLATFORM_FEE: '5%',
                    REVENUE_MODEL: 'weighted',
                    PROJECT_TYPE_SOFTWARE: true,
                    PROJECT_TYPE_GAME: false,
                    PROJECT_TYPE_MUSIC: false,
                    PROJECT_TYPE_FILM: false,
                    PROJECT_TYPE_ART: false
                };
                
                console.log('📋 Variable map created:', Object.keys(variables).length, 'variables');
                
                // Replace variables in template
                let processedTemplate = templateText;
                let replacementCount = 0;
                
                Object.entries(variables).forEach(([key, value]) => {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    const matches = processedTemplate.match(regex);
                    if (matches) {
                        processedTemplate = processedTemplate.replace(regex, String(value));
                        replacementCount += matches.length;
                        console.log(`✅ Replaced {{${key}}} (${matches.length} times) with: ${value}`);
                    }
                });
                
                addResult('✅ Variable Replacement', `Successfully replaced ${replacementCount} variables`, 'success');
                
                // Test 3: Process conditional blocks
                console.log('🧪 Test 3: Testing conditional block processing...');
                
                const conditionalRegex = /{{#IF\s+([^}]+)}}([\s\S]*?){{\/IF}}/g;
                let conditionalMatches = 0;
                let conditionalProcessed = 0;
                
                processedTemplate = processedTemplate.replace(conditionalRegex, (match, condition, content) => {
                    conditionalMatches++;
                    const conditionValue = variables[condition.trim()];
                    
                    if (conditionValue === true || conditionValue === 'true') {
                        console.log(`✅ Including conditional block: ${condition}`);
                        conditionalProcessed++;
                        return content;
                    } else {
                        console.log(`❌ Excluding conditional block: ${condition}`);
                        return '';
                    }
                });
                
                addResult('✅ Conditional Processing', `Processed ${conditionalMatches} conditional blocks, included ${conditionalProcessed}`, 'success');
                
                // Test 4: Verify final result
                console.log('🧪 Test 4: Verifying final processed template...');
                
                const finalLength = processedTemplate.length;
                const hasCompanyName = processedTemplate.includes('Test Company Inc.');
                const hasProjectName = processedTemplate.includes('Test Project');
                const hasContributorName = processedTemplate.includes('John Doe');
                const hasDate = processedTemplate.includes(currentDate);
                
                // Check for remaining unprocessed variables
                const remainingVariables = processedTemplate.match(/{{[^}]+}}/g);
                const remainingCount = remainingVariables ? remainingVariables.length : 0;
                
                console.log('📊 Final template analysis:');
                console.log('  - Final length:', finalLength, 'characters');
                console.log('  - Contains company name:', hasCompanyName);
                console.log('  - Contains project name:', hasProjectName);
                console.log('  - Contains contributor name:', hasContributorName);
                console.log('  - Contains date:', hasDate);
                console.log('  - Remaining unprocessed variables:', remainingCount);
                
                if (remainingVariables) {
                    console.log('  - Unprocessed variables:', remainingVariables.slice(0, 10));
                }
                
                const verificationResults = {
                    finalLength,
                    hasCompanyName,
                    hasProjectName,
                    hasContributorName,
                    hasDate,
                    remainingCount
                };
                
                addResult('✅ Final Verification', 
                    `Length: ${finalLength} chars, Variables replaced: ${hasCompanyName && hasProjectName && hasContributorName && hasDate ? 'Yes' : 'Partial'}, Remaining: ${remainingCount}`, 
                    remainingCount < 10 ? 'success' : 'error');
                
                // Test 5: Show preview
                const preview = processedTemplate.substring(0, 1000) + '...';
                addResult('📄 Agreement Preview', `<pre>${preview}</pre>`, 'info');
                
                statusDiv.innerHTML = '✅ Direct processing test completed successfully!';
                
            } catch (error) {
                console.error('❌ Direct processing test failed:', error);
                addResult('❌ Test Failed', error.message, 'error');
                statusDiv.innerHTML = '❌ Direct processing test failed!';
            }
        }
        
        function addResult(title, content, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<h4>${title}</h4><div>${content}</div>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('test-status').innerHTML = 'Ready to test...';
        }
    </script>
</body>
</html>
