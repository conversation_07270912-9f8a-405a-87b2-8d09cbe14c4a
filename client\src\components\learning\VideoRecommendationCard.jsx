import React, { useState, useContext } from 'react';
import { 
  Card, CardBody, Button, Chip, Avatar, Tooltip,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  Textarea, Select, SelectItem, useDisclosure
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Video Recommendation Card Component
 * 
 * Displays community video recommendations with voting, sharing,
 * and recommendation features.
 */
const VideoRecommendationCard = ({ 
  recommendation, 
  onVote, 
  onRecommend, 
  showRecommendButton = true 
}) => {
  const { currentUser } = useContext(UserContext);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [loading, setLoading] = useState(false);
  const [userVote, setUserVote] = useState(null);
  
  const [recommendForm, setRecommendForm] = useState({
    recommendTo: '',
    reason: '',
    skillContext: ''
  });

  // Handle voting on recommendations
  const handleVote = async (voteType) => {
    if (!currentUser) {
      toast.error('Please log in to vote');
      return;
    }

    try {
      setLoading(true);

      // Check if user already voted
      const { data: existingVote } = await supabase
        .from('video_recommendation_votes')
        .select('*')
        .eq('recommendation_id', recommendation.id)
        .eq('user_id', currentUser.id)
        .single();

      if (existingVote) {
        if (existingVote.vote_type === voteType) {
          // Remove vote if clicking same vote type
          await supabase
            .from('video_recommendation_votes')
            .delete()
            .eq('id', existingVote.id);
          
          setUserVote(null);
          toast.success('Vote removed');
        } else {
          // Update vote type
          await supabase
            .from('video_recommendation_votes')
            .update({ vote_type: voteType })
            .eq('id', existingVote.id);
          
          setUserVote(voteType);
          toast.success(`Changed to ${voteType}`);
        }
      } else {
        // Create new vote
        await supabase
          .from('video_recommendation_votes')
          .insert({
            recommendation_id: recommendation.id,
            user_id: currentUser.id,
            vote_type: voteType
          });
        
        setUserVote(voteType);
        toast.success(`${voteType === 'upvote' ? 'Upvoted' : 'Downvoted'}!`);
      }

      if (onVote) {
        onVote(recommendation.id, voteType);
      }

    } catch (error) {
      console.error('Error voting:', error);
      toast.error('Failed to vote');
    } finally {
      setLoading(false);
    }
  };

  // Handle recommending video to someone
  const handleRecommend = async () => {
    if (!recommendForm.reason.trim()) {
      toast.error('Please provide a reason for the recommendation');
      return;
    }

    try {
      setLoading(true);

      const recommendationData = {
        video_id: recommendation.video_id,
        recommended_by: currentUser.id,
        recommended_to: recommendForm.recommendTo || null,
        skill_context: recommendForm.skillContext,
        recommendation_reason: recommendForm.reason.trim(),
        video_title: recommendation.video_title,
        video_description: recommendation.video_description,
        video_thumbnail: recommendation.video_thumbnail,
        video_duration: recommendation.video_duration,
        is_public: !recommendForm.recommendTo // Public if no specific recipient
      };

      const { error } = await supabase
        .from('video_recommendations')
        .insert([recommendationData]);

      if (error) throw error;

      toast.success('Video recommended successfully!');
      
      if (onRecommend) {
        onRecommend(recommendationData);
      }

      // Reset form
      setRecommendForm({
        recommendTo: '',
        reason: '',
        skillContext: ''
      });
      
      onClose();

    } catch (error) {
      console.error('Error recommending video:', error);
      toast.error('Failed to recommend video');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return `${Math.floor(diffInHours / 168)}w ago`;
  };

  const getVideoThumbnail = () => {
    return recommendation.video_thumbnail || 
           `https://img.youtube.com/vi/${recommendation.video_id}/mqdefault.jpg`;
  };

  const getVideoUrl = () => {
    return `https://www.youtube.com/watch?v=${recommendation.video_id}`;
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="video-recommendation-card hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            {/* Recommender info */}
            <div className="flex items-center gap-3 mb-3">
              <Avatar
                size="sm"
                name={recommendation.recommended_by_name || 'User'}
                className="flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium truncate">
                    {recommendation.recommended_by_name || 'Anonymous'}
                  </span>
                  <span className="text-xs text-default-500">
                    recommended
                  </span>
                  {recommendation.skill_context && (
                    <Chip size="sm" variant="flat" color="primary">
                      {recommendation.skill_context}
                    </Chip>
                  )}
                </div>
                <div className="text-xs text-default-500">
                  {formatTimeAgo(recommendation.created_at)}
                </div>
              </div>
            </div>

            {/* Video preview */}
            <div className="flex gap-3 mb-3">
              <div className="flex-shrink-0">
                <img
                  src={getVideoThumbnail()}
                  alt={recommendation.video_title}
                  className="w-24 h-16 object-cover rounded-lg"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm line-clamp-2 mb-1">
                  {recommendation.video_title || 'Educational Video'}
                </h4>
                {recommendation.video_duration && (
                  <div className="text-xs text-default-500 mb-1">
                    {recommendation.video_duration} minutes
                  </div>
                )}
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  as="a"
                  href={getVideoUrl()}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs"
                >
                  <i className="bi bi-play-circle mr-1"></i>
                  Watch Video
                </Button>
              </div>
            </div>

            {/* Recommendation reason */}
            <div className="mb-3">
              <p className="text-sm text-default-700">
                "{recommendation.recommendation_reason}"
              </p>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Voting buttons */}
                <div className="flex items-center gap-1">
                  <Tooltip content="Upvote this recommendation">
                    <Button
                      size="sm"
                      variant={userVote === 'upvote' ? 'solid' : 'flat'}
                      color={userVote === 'upvote' ? 'success' : 'default'}
                      isIconOnly
                      onClick={() => handleVote('upvote')}
                      isDisabled={loading}
                    >
                      <i className="bi bi-arrow-up"></i>
                    </Button>
                  </Tooltip>
                  
                  <span className="text-sm font-medium min-w-[2rem] text-center">
                    {(recommendation.upvotes || 0) - (recommendation.downvotes || 0)}
                  </span>
                  
                  <Tooltip content="Downvote this recommendation">
                    <Button
                      size="sm"
                      variant={userVote === 'downvote' ? 'solid' : 'flat'}
                      color={userVote === 'downvote' ? 'danger' : 'default'}
                      isIconOnly
                      onClick={() => handleVote('downvote')}
                      isDisabled={loading}
                    >
                      <i className="bi bi-arrow-down"></i>
                    </Button>
                  </Tooltip>
                </div>

                {/* View count */}
                {recommendation.views > 0 && (
                  <div className="flex items-center gap-1 text-xs text-default-500">
                    <i className="bi bi-eye"></i>
                    <span>{recommendation.views}</span>
                  </div>
                )}
              </div>

              {/* Recommend button */}
              {showRecommendButton && currentUser && (
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onClick={onOpen}
                  isDisabled={loading}
                >
                  <i className="bi bi-share mr-1"></i>
                  Recommend
                </Button>
              )}
            </div>

            {/* Featured badge */}
            {recommendation.is_featured && (
              <div className="mt-2">
                <Chip size="sm" color="warning" variant="flat">
                  ⭐ Featured Recommendation
                </Chip>
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>

      {/* Recommend Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Recommend This Video</h3>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-4">
              <div className="bg-default-100 p-3 rounded-lg">
                <h4 className="font-semibold text-sm mb-1">
                  {recommendation.video_title}
                </h4>
                <p className="text-xs text-default-600">
                  You're recommending this video to others in the community
                </p>
              </div>

              <Select
                label="Skill Context (Optional)"
                placeholder="What skill does this video help with?"
                value={recommendForm.skillContext}
                onChange={(e) => setRecommendForm({
                  ...recommendForm,
                  skillContext: e.target.value
                })}
              >
                <SelectItem key="javascript" value="JavaScript">JavaScript</SelectItem>
                <SelectItem key="python" value="Python">Python</SelectItem>
                <SelectItem key="react" value="React">React</SelectItem>
                <SelectItem key="nodejs" value="Node.js">Node.js</SelectItem>
                <SelectItem key="design" value="Design">UI/UX Design</SelectItem>
                <SelectItem key="devops" value="DevOps">DevOps</SelectItem>
              </Select>

              <Textarea
                label="Why are you recommending this video? *"
                placeholder="Explain why others should watch this video..."
                value={recommendForm.reason}
                onChange={(e) => setRecommendForm({
                  ...recommendForm,
                  reason: e.target.value
                })}
                minRows={3}
                isRequired
              />
            </div>
          </ModalBody>
          
          <ModalFooter>
            <Button
              color="danger"
              variant="flat"
              onPress={onClose}
              isDisabled={loading}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleRecommend}
              isLoading={loading}
            >
              Share Recommendation
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default VideoRecommendationCard;
