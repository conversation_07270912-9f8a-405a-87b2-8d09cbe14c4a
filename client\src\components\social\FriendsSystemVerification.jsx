import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Tabs, Tab, <PERSON>, Badge,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  Input, Textarea, Avatar, Divider, Spinner, useDisclosure
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Friends System Verification Component
 * 
 * Tests and verifies that the friends system is working properly:
 * - Friend requests (send/receive/accept/decline)
 * - Friends list display
 * - Profile viewing
 * - Social interactions
 * - Messaging integration
 */
const FriendsSystemVerification = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('friends');
  const [friends, setFriends] = useState([]);
  const [friendRequests, setFriendRequests] = useState([]);
  const [sentRequests, setSentRequests] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [requestForm, setRequestForm] = useState({
    recipientEmail: '',
    message: ''
  });

  const [systemStatus, setSystemStatus] = useState({
    friendsTable: false,
    requestsTable: false,
    profilesTable: false,
    messagesTable: false,
    functionsWorking: false
  });

  useEffect(() => {
    if (currentUser) {
      verifySystemTables();
      loadFriendsData();
    }
  }, [currentUser]);

  // Verify database tables exist and are accessible
  const verifySystemTables = async () => {
    try {
      // Test friends table
      const { error: friendsError } = await supabase
        .from('user_allies')
        .select('id')
        .limit(1);
      
      // Test friend requests table
      const { error: requestsError } = await supabase
        .from('friend_requests')
        .select('id')
        .limit(1);

      // Test user profiles table
      const { error: profilesError } = await supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      // Test messages table
      const { error: messagesError } = await supabase
        .from('messages')
        .select('id')
        .limit(1);

      setSystemStatus({
        friendsTable: !friendsError,
        requestsTable: !requestsError,
        profilesTable: !profilesError,
        messagesTable: !messagesError,
        functionsWorking: true // We'll test this with actual operations
      });

    } catch (error) {
      console.error('Error verifying system tables:', error);
    }
  };

  // Load all friends-related data
  const loadFriendsData = async () => {
    try {
      setLoading(true);
      
      // Load friends (accepted connections)
      const { data: friendsData, error: friendsError } = await supabase
        .from('user_allies')
        .select(`
          *,
          ally:auth.users!user_allies_ally_id_fkey(
            id,
            email,
            user_metadata
          ),
          user:auth.users!user_allies_user_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .or(`user_id.eq.${currentUser.id},ally_id.eq.${currentUser.id}`)
        .eq('status', 'accepted');

      if (friendsError) throw friendsError;

      // Process friends data to get the other person in each relationship
      const processedFriends = friendsData?.map(connection => {
        const isUserInitiator = connection.user_id === currentUser.id;
        const friend = isUserInitiator ? connection.ally : connection.user;
        return {
          ...connection,
          friend,
          friendName: friend?.user_metadata?.display_name || friend?.email,
          friendAvatar: friend?.user_metadata?.avatar_url
        };
      }) || [];

      setFriends(processedFriends);

      // Load incoming friend requests
      const { data: requestsData, error: requestsError } = await supabase
        .from('friend_requests')
        .select(`
          *,
          sender:auth.users!friend_requests_sender_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .eq('recipient_id', currentUser.id)
        .eq('status', 'pending');

      if (requestsError) throw requestsError;
      setFriendRequests(requestsData || []);

      // Load sent friend requests
      const { data: sentData, error: sentError } = await supabase
        .from('friend_requests')
        .select(`
          *,
          recipient:auth.users!friend_requests_recipient_id_fkey(
            id,
            email,
            user_metadata
          )
        `)
        .eq('sender_id', currentUser.id)
        .eq('status', 'pending');

      if (sentError) throw sentError;
      setSentRequests(sentData || []);

    } catch (error) {
      console.error('Error loading friends data:', error);
      toast.error('Failed to load friends data');
    } finally {
      setLoading(false);
    }
  };

  // Search for users to add as friends
  const searchUsers = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('auth.users')
        .select('id, email, user_metadata')
        .or(`email.ilike.%${searchTerm}%,user_metadata->>display_name.ilike.%${searchTerm}%`)
        .neq('id', currentUser.id)
        .limit(10);

      if (error) throw error;
      setSearchResults(data || []);

    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Failed to search users');
    }
  };

  // Send friend request
  const sendFriendRequest = async (recipientId, recipientEmail) => {
    try {
      const requestData = {
        sender_id: currentUser.id,
        recipient_id: recipientId,
        recipient_email: recipientEmail,
        message: requestForm.message.trim(),
        request_type: 'friend',
        status: 'pending'
      };

      const { error } = await supabase
        .from('friend_requests')
        .insert([requestData]);

      if (error) throw error;

      toast.success('Friend request sent!');
      setRequestForm({ recipientEmail: '', message: '' });
      onClose();
      loadFriendsData();

    } catch (error) {
      console.error('Error sending friend request:', error);
      toast.error('Failed to send friend request');
    }
  };

  // Accept friend request
  const acceptFriendRequest = async (requestId, senderId) => {
    try {
      // Update request status
      const { error: updateError } = await supabase
        .from('friend_requests')
        .update({ 
          status: 'accepted',
          accepted_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) throw updateError;

      // Create ally relationship
      const { error: allyError } = await supabase
        .from('user_allies')
        .insert([{
          user_id: currentUser.id,
          ally_id: senderId,
          status: 'accepted',
          connection_type: 'friend',
          accepted_at: new Date().toISOString(),
          created_by: senderId
        }]);

      if (allyError) throw allyError;

      toast.success('Friend request accepted!');
      loadFriendsData();

    } catch (error) {
      console.error('Error accepting friend request:', error);
      toast.error('Failed to accept friend request');
    }
  };

  // Decline friend request
  const declineFriendRequest = async (requestId) => {
    try {
      const { error } = await supabase
        .from('friend_requests')
        .update({ 
          status: 'declined',
          declined_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;

      toast.success('Friend request declined');
      loadFriendsData();

    } catch (error) {
      console.error('Error declining friend request:', error);
      toast.error('Failed to decline friend request');
    }
  };

  // Test messaging functionality
  const testMessaging = async (friendId) => {
    try {
      const testMessage = {
        to_user_id: friendId,
        content: 'Test message from friends system verification',
        message_type: 'text'
      };

      const response = await fetch('/.netlify/functions/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify(testMessage)
      });

      if (response.ok) {
        toast.success('Messaging system working!');
        setSystemStatus(prev => ({ ...prev, functionsWorking: true }));
      } else {
        throw new Error('Messaging function failed');
      }

    } catch (error) {
      console.error('Error testing messaging:', error);
      toast.error('Messaging system not working');
      setSystemStatus(prev => ({ ...prev, functionsWorking: false }));
    }
  };

  const getSystemStatusColor = (status) => status ? 'success' : 'danger';
  const getSystemStatusIcon = (status) => status ? 'bi-check-circle' : 'bi-x-circle';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2">Verifying friends system...</span>
      </div>
    );
  }

  return (
    <div className="friends-system-verification space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Friends System Verification</h2>
          <p className="text-default-600">Testing social features and connections</p>
        </div>
        
        <Button color="primary" onClick={onOpen}>
          <i className="bi bi-person-plus mr-1"></i>
          Add Friend
        </Button>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">System Status</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <Chip
                color={getSystemStatusColor(systemStatus.friendsTable)}
                variant="flat"
                startContent={<i className={getSystemStatusIcon(systemStatus.friendsTable)}></i>}
              >
                Friends Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getSystemStatusColor(systemStatus.requestsTable)}
                variant="flat"
                startContent={<i className={getSystemStatusIcon(systemStatus.requestsTable)}></i>}
              >
                Requests Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getSystemStatusColor(systemStatus.profilesTable)}
                variant="flat"
                startContent={<i className={getSystemStatusIcon(systemStatus.profilesTable)}></i>}
              >
                Profiles Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getSystemStatusColor(systemStatus.messagesTable)}
                variant="flat"
                startContent={<i className={getSystemStatusIcon(systemStatus.messagesTable)}></i>}
              >
                Messages Table
              </Chip>
            </div>
            <div className="text-center">
              <Chip
                color={getSystemStatusColor(systemStatus.functionsWorking)}
                variant="flat"
                startContent={<i className={getSystemStatusIcon(systemStatus.functionsWorking)}></i>}
              >
                API Functions
              </Chip>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Friends Data */}
      <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab}>
        <Tab key="friends" title={`Friends (${friends.length})`}>
          <div className="mt-4 space-y-4">
            {friends.length === 0 ? (
              <div className="text-center py-8 text-default-600">
                <i className="bi bi-people text-4xl mb-2 block"></i>
                <p>No friends yet</p>
                <p className="text-sm">Start connecting with other users</p>
              </div>
            ) : (
              friends.map((friend) => (
                <Card key={friend.id} className="hover:shadow-md transition-shadow">
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={friend.friendAvatar}
                          name={friend.friendName}
                          size="md"
                        />
                        <div>
                          <h4 className="font-semibold">{friend.friendName}</h4>
                          <p className="text-sm text-default-600">
                            Connected {new Date(friend.accepted_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          color="primary"
                          variant="flat"
                          onClick={() => testMessaging(friend.friend.id)}
                        >
                          <i className="bi bi-chat mr-1"></i>
                          Test Message
                        </Button>
                        <Button
                          size="sm"
                          color="default"
                          variant="flat"
                        >
                          <i className="bi bi-person mr-1"></i>
                          View Profile
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>
        
        <Tab key="requests" title={`Requests (${friendRequests.length})`}>
          <div className="mt-4 space-y-4">
            {friendRequests.length === 0 ? (
              <div className="text-center py-8 text-default-600">
                <i className="bi bi-inbox text-4xl mb-2 block"></i>
                <p>No pending friend requests</p>
              </div>
            ) : (
              friendRequests.map((request) => (
                <Card key={request.id} className="hover:shadow-md transition-shadow">
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={request.sender?.user_metadata?.avatar_url}
                          name={request.sender?.user_metadata?.display_name || request.sender?.email}
                          size="md"
                        />
                        <div>
                          <h4 className="font-semibold">
                            {request.sender?.user_metadata?.display_name || request.sender?.email}
                          </h4>
                          <p className="text-sm text-default-600">
                            Sent {new Date(request.sent_at).toLocaleDateString()}
                          </p>
                          {request.message && (
                            <p className="text-sm text-default-700 mt-1">"{request.message}"</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          color="success"
                          onClick={() => acceptFriendRequest(request.id, request.sender_id)}
                        >
                          Accept
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          variant="flat"
                          onClick={() => declineFriendRequest(request.id)}
                        >
                          Decline
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>
        
        <Tab key="sent" title={`Sent (${sentRequests.length})`}>
          <div className="mt-4 space-y-4">
            {sentRequests.length === 0 ? (
              <div className="text-center py-8 text-default-600">
                <i className="bi bi-send text-4xl mb-2 block"></i>
                <p>No sent requests</p>
              </div>
            ) : (
              sentRequests.map((request) => (
                <Card key={request.id}>
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={request.recipient?.user_metadata?.avatar_url}
                          name={request.recipient?.user_metadata?.display_name || request.recipient?.email || request.recipient_email}
                          size="md"
                        />
                        <div>
                          <h4 className="font-semibold">
                            {request.recipient?.user_metadata?.display_name || request.recipient?.email || request.recipient_email}
                          </h4>
                          <p className="text-sm text-default-600">
                            Sent {new Date(request.sent_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <Chip color="warning" variant="flat">
                        Pending
                      </Chip>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>
      </Tabs>

      {/* Add Friend Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Add Friend</h3>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-4">
              <div>
                <Input
                  label="Search Users"
                  placeholder="Enter email or name"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchUsers()}
                  endContent={
                    <Button
                      size="sm"
                      color="primary"
                      variant="flat"
                      onClick={searchUsers}
                    >
                      Search
                    </Button>
                  }
                />
              </div>

              {searchResults.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-semibold">Search Results:</h4>
                  {searchResults.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <Avatar
                          src={user.user_metadata?.avatar_url}
                          name={user.user_metadata?.display_name || user.email}
                          size="sm"
                        />
                        <span>{user.user_metadata?.display_name || user.email}</span>
                      </div>
                      <Button
                        size="sm"
                        color="primary"
                        onClick={() => sendFriendRequest(user.id, user.email)}
                      >
                        Add Friend
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <Divider />

              <div>
                <Input
                  label="Or invite by email"
                  placeholder="<EMAIL>"
                  value={requestForm.recipientEmail}
                  onChange={(e) => setRequestForm({ 
                    ...requestForm, 
                    recipientEmail: e.target.value 
                  })}
                />
              </div>

              <Textarea
                label="Message (Optional)"
                placeholder="Add a personal message..."
                value={requestForm.message}
                onChange={(e) => setRequestForm({ 
                  ...requestForm, 
                  message: e.target.value 
                })}
                minRows={2}
              />
            </div>
          </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={() => sendFriendRequest(null, requestForm.recipientEmail)}
              isDisabled={!requestForm.recipientEmail.trim()}
            >
              Send Request
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default FriendsSystemVerification;
