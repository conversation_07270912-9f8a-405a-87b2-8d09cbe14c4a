/**
 * Project Detail Fix Verification Test
 * 
 * Tests to verify that the project detail page now displays content properly
 * after adding CSS styles to fix the blank content area issue
 */

import { test, expect } from '@playwright/test';

test.describe('Project Detail Fix Verification', () => {
  test('should display project detail page content properly', async ({ page }) => {
    console.log('🔍 Starting project detail fix verification test...');

    // Navigate to the site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Check if we need to log in - look for direct login form (no landing page)
    const emailInput = page.locator('input[type="email"], input[name="email"], #email').first();
    const isLoginFormVisible = await emailInput.isVisible();

    if (isLoginFormVisible) {
      console.log('🔐 Found direct login form, logging in...');

      // Fill in credentials
      await emailInput.fill('<EMAIL>');
      await page.locator('input[type="password"], input[name="password"], #password').first().fill('TestPassword123!');

      // Submit login
      await page.click('button[type="submit"], button:has-text("Sign In"), button:has-text("Log In")');
      await page.waitForLoadState('networkidle');

      console.log('✅ Logged in successfully');
    } else {
      // Check if already logged in
      const isLoggedIn = await page.locator('[data-testid="user-profile"], .user-profile, [aria-label*="profile"], [aria-label*="Profile"]').isVisible();
      if (isLoggedIn) {
        console.log('✅ Already logged in');
      } else {
        console.log('⚠️ Could not find login form or user profile, continuing anyway');
      }
    }

    // Navigate to projects list first
    console.log('📋 Navigating to projects list...');
    await page.goto('https://royalty.technology/projects');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/fix-verification-01-projects-list.png', fullPage: true });

    // Find the first project link
    const projectLinks = await page.locator('a[href*="/project/"]').all();
    console.log(`Found ${projectLinks.length} project links`);

    if (projectLinks.length > 0) {
      // Get the first project URL
      const firstProjectUrl = await projectLinks[0].getAttribute('href');
      console.log(`🔗 First project URL: ${firstProjectUrl}`);

      // Navigate to the project detail page
      await page.goto(`https://royalty.technology${firstProjectUrl}`);
      await page.waitForLoadState('networkidle');
      
      // Wait a bit for any animations or loading
      await page.waitForTimeout(2000);
      
      await page.screenshot({ path: 'test-results/fix-verification-02-project-detail.png', fullPage: true });

      // Check for key elements that should now be visible
      console.log('🔍 Checking for project detail elements...');

      // Check for project container
      const projectContainer = await page.locator('.project-detail-container').isVisible();
      console.log(`✅ Project container visible: ${projectContainer}`);
      expect(projectContainer).toBe(true);

      // Check for project header
      const projectHeader = await page.locator('.project-header').isVisible();
      console.log(`✅ Project header visible: ${projectHeader}`);
      expect(projectHeader).toBe(true);

      // Check for project title
      const projectTitle = await page.locator('.project-title, h1').isVisible();
      console.log(`✅ Project title visible: ${projectTitle}`);
      expect(projectTitle).toBe(true);

      // Check for project tabs
      const projectTabs = await page.locator('.project-tabs').isVisible();
      console.log(`✅ Project tabs visible: ${projectTabs}`);
      expect(projectTabs).toBe(true);

      // Check for tab buttons
      const tabButtons = await page.locator('.tab-button').count();
      console.log(`✅ Tab buttons count: ${tabButtons}`);
      expect(tabButtons).toBeGreaterThan(0);

      // Check for project content area
      const projectContent = await page.locator('.project-content').isVisible();
      console.log(`✅ Project content area visible: ${projectContent}`);
      expect(projectContent).toBe(true);

      // Check for overview content (default tab)
      const overviewContent = await page.locator('.project-overview').isVisible();
      console.log(`✅ Overview content visible: ${overviewContent}`);
      expect(overviewContent).toBe(true);

      // Check for project actions (buttons)
      const projectActions = await page.locator('.project-actions').isVisible();
      console.log(`✅ Project actions visible: ${projectActions}`);
      expect(projectActions).toBe(true);

      // Test tab navigation
      console.log('🔄 Testing tab navigation...');
      
      // Click on contributions tab if it exists
      const contributionsTab = page.locator('.tab-button:has-text("Contributions")');
      if (await contributionsTab.isVisible()) {
        await contributionsTab.click();
        await page.waitForTimeout(500);
        
        const contributionsContent = await page.locator('.project-contributions').isVisible();
        console.log(`✅ Contributions tab content visible: ${contributionsContent}`);
        
        await page.screenshot({ path: 'test-results/fix-verification-03-contributions-tab.png', fullPage: true });
      }

      // Click on revenue tab if it exists
      const revenueTab = page.locator('.tab-button:has-text("Revenue")');
      if (await revenueTab.isVisible()) {
        await revenueTab.click();
        await page.waitForTimeout(500);
        
        const revenueContent = await page.locator('.project-revenue').isVisible();
        console.log(`✅ Revenue tab content visible: ${revenueContent}`);
        
        await page.screenshot({ path: 'test-results/fix-verification-04-revenue-tab.png', fullPage: true });
      }

      // Go back to overview tab
      const overviewTab = page.locator('.tab-button:has-text("Overview")');
      if (await overviewTab.isVisible()) {
        await overviewTab.click();
        await page.waitForTimeout(500);
        
        await page.screenshot({ path: 'test-results/fix-verification-05-back-to-overview.png', fullPage: true });
      }

      // Test agreements button if user has admin role
      const agreementsButton = page.locator('a[href*="/agreements"]:has-text("Agreements")');
      if (await agreementsButton.isVisible()) {
        console.log('🔗 Testing agreements navigation...');
        
        await agreementsButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        await page.screenshot({ path: 'test-results/fix-verification-06-agreements-page.png', fullPage: true });
        
        // Check if we're on the agreements page
        const currentUrl = page.url();
        console.log(`Current URL after clicking agreements: ${currentUrl}`);
        
        if (currentUrl.includes('/agreements')) {
          console.log('✅ Successfully navigated to agreements page');
          
          // Check for agreement manager or content
          const agreementContent = await page.locator('.agreement-manager, .agreements-container, h1, h2').isVisible();
          console.log(`✅ Agreement page content visible: ${agreementContent}`);
        }
      }

      console.log('🎉 Project detail page fix verification completed successfully!');
      console.log('✅ All key elements are now visible and functional');

    } else {
      console.log('❌ No project links found in projects list');
      
      // Try to navigate to the specific project the user mentioned
      console.log('🔄 Trying to navigate to the specific project the user mentioned...');
      await page.goto('https://royalty.technology/#/project/8405eda8-da7e-4cef-a06e-2273fa46fed8');
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: 'test-results/fix-verification-07-direct-navigation.png', fullPage: true });
      
      // Check if we get a "not found" page with proper styling
      const notFoundContent = await page.locator('.project-not-found').isVisible();
      console.log(`✅ Project not found page styled properly: ${notFoundContent}`);
    }

    console.log('🎉 Project detail fix verification test completed');
  });
});
