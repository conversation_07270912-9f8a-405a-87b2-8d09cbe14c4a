import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Chip,
  Divider,
  RadioGroup,
  Radio
} from '@heroui/react';
import { MessageSquare, Send, Star, Lightbulb, ThumbsUp, ThumbsDown } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const GeneralFeedback = () => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    type: '',
    category: '',
    title: '',
    description: '',
    rating: '',
    email: user?.email || '',
    anonymous: false
  });

  const feedbackTypes = [
    { key: 'suggestion', label: 'Suggestion', icon: Lightbulb, color: 'primary' },
    { key: 'compliment', label: 'Compliment', icon: ThumbsUp, color: 'success' },
    { key: 'complaint', label: 'Complaint', icon: ThumbsDown, color: 'warning' },
    { key: 'feature_request', label: 'Feature Request', icon: Star, color: 'secondary' },
    { key: 'general', label: 'General Feedback', icon: MessageSquare, color: 'default' }
  ];

  const categoryOptions = [
    { key: 'ui_ux', label: 'User Interface & Experience' },
    { key: 'performance', label: 'Performance' },
    { key: 'features', label: 'Features & Functionality' },
    { key: 'content', label: 'Content & Documentation' },
    { key: 'support', label: 'Customer Support' },
    { key: 'billing', label: 'Billing & Pricing' },
    { key: 'mobile', label: 'Mobile Experience' },
    { key: 'integration', label: 'Integrations' },
    { key: 'security', label: 'Security & Privacy' },
    { key: 'other', label: 'Other' }
  ];

  const ratingOptions = [
    { key: '5', label: 'Excellent (5/5)' },
    { key: '4', label: 'Good (4/5)' },
    { key: '3', label: 'Average (3/5)' },
    { key: '2', label: 'Poor (2/5)' },
    { key: '1', label: 'Very Poor (1/5)' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.type || !formData.title || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('feedback')
        .insert([{
          type: formData.type,
          category: formData.category,
          title: formData.title,
          description: formData.description,
          rating: formData.rating ? parseInt(formData.rating) : null,
          email: formData.anonymous ? null : formData.email,
          user_id: formData.anonymous ? null : user?.id,
          is_anonymous: formData.anonymous,
          status: 'new',
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;

      toast.success('Thank you for your feedback! We appreciate your input.');
      
      // Reset form
      setFormData({
        type: '',
        category: '',
        title: '',
        description: '',
        rating: '',
        email: user?.email || '',
        anonymous: false
      });

    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTypeIcon = (type) => {
    const typeObj = feedbackTypes.find(t => t.key === type);
    return typeObj ? <typeObj.icon size={20} /> : <MessageSquare size={20} />;
  };

  const getTypeColor = (type) => {
    const typeObj = feedbackTypes.find(t => t.key === type);
    return typeObj ? typeObj.color : 'default';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 max-w-4xl mx-auto"
    >
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
              <MessageSquare size={24} className="text-primary" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">General Feedback</h2>
              <p className="text-white/70">Share your thoughts and help us improve Royaltea</p>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Feedback Type */}
            <div className="space-y-3">
              <label className="text-white/70 text-sm font-medium">Feedback Type *</label>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3">
                {feedbackTypes.map((type) => (
                  <Button
                    key={type.key}
                    variant={formData.type === type.key ? "solid" : "bordered"}
                    color={formData.type === type.key ? type.color : "default"}
                    className="h-auto p-4 justify-start"
                    onPress={() => handleInputChange('type', type.key)}
                  >
                    <div className="flex flex-col items-center gap-2 text-center">
                      <type.icon size={20} />
                      <span className="text-xs">{type.label}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Title and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Feedback Title"
                placeholder="Brief summary of your feedback"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                isRequired
                variant="bordered"
                classNames={{
                  input: "text-white",
                  label: "text-white/70"
                }}
              />

              <Select
                label="Category"
                placeholder="Select a category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                variant="bordered"
                classNames={{
                  label: "text-white/70",
                  value: "text-white"
                }}
              >
                {categoryOptions.map((option) => (
                  <SelectItem key={option.key} value={option.key}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            {/* Description */}
            <Textarea
              label="Detailed Feedback"
              placeholder="Please provide detailed feedback..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              isRequired
              variant="bordered"
              minRows={5}
              classNames={{
                input: "text-white",
                label: "text-white/70"
              }}
            />

            {/* Rating (optional) */}
            <div className="space-y-3">
              <label className="text-white/70 text-sm font-medium">Overall Rating (Optional)</label>
              <RadioGroup
                value={formData.rating}
                onValueChange={(value) => handleInputChange('rating', value)}
                orientation="horizontal"
                classNames={{
                  wrapper: "gap-4"
                }}
              >
                {ratingOptions.map((option) => (
                  <Radio key={option.key} value={option.key} classNames={{
                    base: "text-white/80"
                  }}>
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={16}
                            className={i < parseInt(option.key) ? "text-warning fill-warning" : "text-white/30"}
                          />
                        ))}
                      </div>
                      <span className="text-sm">{option.label}</span>
                    </div>
                  </Radio>
                ))}
              </RadioGroup>
            </div>

            <Divider className="bg-white/20" />

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Contact Information</h3>
              
              <div className="flex items-center gap-4">
                <input
                  type="checkbox"
                  id="anonymous"
                  checked={formData.anonymous}
                  onChange={(e) => handleInputChange('anonymous', e.target.checked)}
                  className="w-4 h-4 text-primary bg-transparent border-white/30 rounded focus:ring-primary"
                />
                <label htmlFor="anonymous" className="text-white/80">
                  Submit anonymously (we won't be able to follow up)
                </label>
              </div>

              {!formData.anonymous && (
                <Input
                  label="Email Address"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  variant="bordered"
                  description="We'll use this to follow up if needed"
                  classNames={{
                    input: "text-white",
                    label: "text-white/70",
                    description: "text-white/50"
                  }}
                />
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                color={getTypeColor(formData.type)}
                size="lg"
                startContent={formData.type ? getTypeIcon(formData.type) : <Send size={20} />}
                isLoading={isSubmitting}
                className="min-w-[150px]"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>

      {/* Feedback Guidelines */}
      <Card className="mt-6 bg-white/5 backdrop-blur-md border-white/10">
        <CardBody className="p-4">
          <h3 className="text-white font-semibold mb-3">Feedback Guidelines</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-white/70">
            <div>
              <h4 className="text-white/90 font-medium mb-2">What makes good feedback:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Be specific and detailed</li>
                <li>Include steps to reproduce issues</li>
                <li>Suggest potential solutions</li>
                <li>Be constructive and respectful</li>
              </ul>
            </div>
            <div>
              <h4 className="text-white/90 font-medium mb-2">Response time:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Bug reports: 1-2 business days</li>
                <li>Feature requests: 1 week</li>
                <li>General feedback: 3-5 business days</li>
                <li>Urgent issues: Same day</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default GeneralFeedback;
