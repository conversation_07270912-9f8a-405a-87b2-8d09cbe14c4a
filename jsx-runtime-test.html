<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSX Runtime Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 JSX Runtime Fix Verification</h1>
        <p>This page will test if the JSX runtime error has been resolved in the Royaltea project wizard.</p>
        
        <div id="test-status" class="status info">
            ⏳ Initializing test...
        </div>
        
        <button onclick="testProjectWizard()">🧪 Test Project Wizard</button>
        <button onclick="clearConsole()">🧹 Clear Console</button>
        <button onclick="checkMainBundle()">📦 Check Main Bundle</button>
        
        <h3>Console Output:</h3>
        <div id="console-output"></div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let testStatus = document.getElementById('test-status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            consoleOutput.textContent += logEntry;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            testStatus.textContent = message;
            testStatus.className = `status ${type}`;
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        async function checkMainBundle() {
            log('🔍 Checking main bundle for JSX runtime configuration...');
            updateStatus('Checking main bundle...', 'info');
            
            try {
                // Try to fetch the main bundle
                const response = await fetch('/assets/main-Bz7HvJ6N.js');
                if (response.ok) {
                    log('✅ Main bundle loaded successfully');
                    log('📦 Bundle hash: main-Bz7HvJ6N.js (latest)');
                    updateStatus('Main bundle loaded successfully', 'success');
                } else {
                    log('❌ Failed to load main bundle');
                    updateStatus('Failed to load main bundle', 'error');
                }
            } catch (error) {
                log(`❌ Error checking main bundle: ${error.message}`);
                updateStatus('Error checking main bundle', 'error');
            }
        }
        
        async function testProjectWizard() {
            log('🚀 Starting JSX Runtime Test...');
            updateStatus('Testing project wizard...', 'info');
            
            // Create iframe to test the wizard page
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'https://royalty.technology/project/wizard';
            
            let jsxErrors = [];
            let loadTimeout;
            
            // Set up error monitoring
            window.addEventListener('error', (event) => {
                if (event.filename && event.filename.includes('main-')) {
                    const errorMsg = `JS Error: ${event.message} at ${event.filename}:${event.lineno}`;
                    log(`❌ ${errorMsg}`);
                    jsxErrors.push(errorMsg);
                }
            });
            
            iframe.onload = () => {
                clearTimeout(loadTimeout);
                
                setTimeout(() => {
                    // Check for JSX runtime errors
                    const hasJSXErrors = jsxErrors.some(error => 
                        error.includes('jsxDEV') || 
                        error.includes('React is not defined') ||
                        error.includes('jsx')
                    );
                    
                    if (hasJSXErrors) {
                        log('❌ JSX Runtime errors detected:');
                        jsxErrors.forEach(error => log(`  - ${error}`));
                        updateStatus('JSX Runtime errors still present', 'error');
                    } else {
                        log('✅ No JSX Runtime errors detected!');
                        log('🎉 JSX Runtime fix appears to be successful');
                        updateStatus('JSX Runtime fix successful!', 'success');
                    }
                    
                    // Try to access iframe content (if same origin)
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const hasReactElements = iframeDoc.querySelectorAll('[data-reactroot], [data-testid]').length > 0;
                        const hasInputs = iframeDoc.querySelectorAll('input, button').length > 0;
                        
                        log(`📊 Component Analysis:`);
                        log(`  - React elements found: ${hasReactElements}`);
                        log(`  - Input elements found: ${hasInputs}`);
                        
                        if (hasInputs) {
                            log('✅ Wizard form elements detected - components are rendering!');
                            updateStatus('Components rendering successfully!', 'success');
                        } else {
                            log('⚠️ No form elements detected - may be routing issue');
                            updateStatus('Components may not be rendering properly', 'warning');
                        }
                    } catch (e) {
                        log('ℹ️ Cannot access iframe content (cross-origin)');
                        log('ℹ️ This is normal for production testing');
                    }
                    
                    document.body.removeChild(iframe);
                }, 3000);
            };
            
            iframe.onerror = () => {
                clearTimeout(loadTimeout);
                log('❌ Failed to load project wizard page');
                updateStatus('Failed to load wizard page', 'error');
                document.body.removeChild(iframe);
            };
            
            loadTimeout = setTimeout(() => {
                log('⏰ Test timeout - removing iframe');
                updateStatus('Test timeout', 'warning');
                document.body.removeChild(iframe);
            }, 10000);
            
            document.body.appendChild(iframe);
            log('📍 Loading project wizard in iframe...');
        }
        
        // Initialize
        log('🔧 JSX Runtime Test Tool Initialized');
        log('📝 Ready to test the project wizard JSX runtime fix');
        updateStatus('Ready to test', 'info');
    </script>
</body>
</html>
