import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Badge } from '@heroui/react';
import { Play, CheckCircle, Clock, BookOpen } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const InteractiveTutorials = () => {
  const { currentUser } = useAuth();
  const [tutorials, setTutorials] = useState([]);
  const [userProgress, setUserProgress] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTutorials();
  }, [currentUser]);

  const loadTutorials = async () => {
    try {
      setLoading(true);

      // Load available tutorials
      const { data: tutorialData, error: tutorialError } = await supabase
        .from('learning_content')
        .select('*')
        .eq('content_type', 'tutorial')
        .eq('is_active', true)
        .order('difficulty_level', { ascending: true });

      if (tutorialError) throw tutorialError;

      // Load user progress
      const { data: progressData, error: progressError } = await supabase
        .from('user_learning_progress')
        .select('*')
        .eq('user_id', currentUser.id);

      if (progressError) throw progressError;

      // Create progress map
      const progressMap = {};
      progressData?.forEach(progress => {
        progressMap[progress.content_id] = progress;
      });

      setTutorials(tutorialData || []);
      setUserProgress(progressMap);

    } catch (error) {
      console.error('Error loading tutorials:', error);
      toast.error('Failed to load tutorials');
    } finally {
      setLoading(false);
    }
  };

  const startTutorial = async (tutorialId) => {
    try {
      // Record tutorial start
      const { error } = await supabase
        .from('user_learning_progress')
        .upsert({
          user_id: currentUser.id,
          content_id: tutorialId,
          status: 'in_progress',
          started_at: new Date().toISOString(),
          progress_percentage: 0
        });

      if (error) throw error;

      toast.success('Tutorial started!');
      loadTutorials(); // Refresh progress

    } catch (error) {
      console.error('Error starting tutorial:', error);
      toast.error('Failed to start tutorial');
    }
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'danger';
      default: return 'default';
    }
  };

  const getStatusIcon = (tutorial) => {
    const progress = userProgress[tutorial.id];
    if (!progress) return <Play size={16} />;
    if (progress.status === 'completed') return <CheckCircle size={16} className="text-green-500" />;
    return <Clock size={16} className="text-yellow-500" />;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Interactive Tutorials</h2>
        <p className="text-white/70">Learn platform features through hands-on tutorials</p>
      </div>

      {tutorials.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <BookOpen size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Tutorials Available</h3>
            <p className="text-gray-600">Check back later for new learning content.</p>
          </CardBody>
        </Card>
      ) : (
        <div className="grid gap-4">
          {tutorials.map((tutorial) => {
            const progress = userProgress[tutorial.id];
            const isCompleted = progress?.status === 'completed';
            const isInProgress = progress?.status === 'in_progress';
            const progressPercentage = progress?.progress_percentage || 0;

            return (
              <Card key={tutorial.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-1">{tutorial.title}</h3>
                      <p className="text-gray-600 text-sm">{tutorial.description}</p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Badge 
                        color={getDifficultyColor(tutorial.difficulty_level)} 
                        variant="flat" 
                        size="sm"
                      >
                        {tutorial.difficulty_level}
                      </Badge>
                      {getStatusIcon(tutorial)}
                    </div>
                  </div>
                </CardHeader>
                <CardBody className="pt-2">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>⏱️ {tutorial.estimated_duration} min</span>
                      <span>📚 {tutorial.category}</span>
                    </div>
                  </div>

                  {isInProgress && (
                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Progress</span>
                        <span>{progressPercentage}%</span>
                      </div>
                      <Progress value={progressPercentage} color="primary" size="sm" />
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                      {tutorial.prerequisites && (
                        <span>Prerequisites: {tutorial.prerequisites}</span>
                      )}
                    </div>
                    <Button
                      color={isCompleted ? "success" : "primary"}
                      variant={isCompleted ? "flat" : "solid"}
                      size="sm"
                      startContent={getStatusIcon(tutorial)}
                      onClick={() => startTutorial(tutorial.id)}
                      disabled={isCompleted}
                    >
                      {isCompleted ? 'Completed' : isInProgress ? 'Continue' : 'Start Tutorial'}
                    </Button>
                  </div>
                </CardBody>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default InteractiveTutorials;
