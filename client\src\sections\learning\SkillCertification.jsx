import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Badge, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { Award, Clock, CheckCircle, Play, Trophy } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const SkillCertification = () => {
  const { currentUser } = useAuth();
  const [certifications, setCertifications] = useState([]);
  const [userCertifications, setUserCertifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCert, setSelectedCert] = useState(null);
  const [showCertModal, setShowCertModal] = useState(false);

  useEffect(() => {
    loadCertifications();
  }, [currentUser]);

  const loadCertifications = async () => {
    try {
      setLoading(true);

      // Load available certifications
      const { data: certsData, error: certsError } = await supabase
        .from('skill_certifications')
        .select('*')
        .eq('is_active', true)
        .order('difficulty_level', { ascending: true });

      if (certsError) throw certsError;

      // Load user certifications
      const { data: userCertsData, error: userCertsError } = await supabase
        .from('user_certifications')
        .select('*')
        .eq('user_id', currentUser.id);

      if (userCertsError) throw userCertsError;

      setCertifications(certsData || getStaticCertifications());
      setUserCertifications(userCertsData || []);

    } catch (error) {
      console.error('Error loading certifications:', error);
      setCertifications(getStaticCertifications());
    } finally {
      setLoading(false);
    }
  };

  const getStaticCertifications = () => [
    {
      id: 'platform-basics',
      title: 'Platform Basics Certification',
      description: 'Master the fundamentals of using the Royaltea platform',
      skill_area: 'platform',
      difficulty_level: 'beginner',
      estimated_hours: 2,
      requirements: ['Complete getting started tutorial', 'Create first project'],
      badge_icon: '🏆',
      points_awarded: 100
    },
    {
      id: 'project-management',
      title: 'Project Management Certification',
      description: 'Learn advanced project management techniques and tools',
      skill_area: 'management',
      difficulty_level: 'intermediate',
      estimated_hours: 8,
      requirements: ['Complete 3 projects', 'Lead a team of 3+ members'],
      badge_icon: '📋',
      points_awarded: 250
    },
    {
      id: 'team-leadership',
      title: 'Team Leadership Certification',
      description: 'Develop skills for effective team leadership and collaboration',
      skill_area: 'leadership',
      difficulty_level: 'advanced',
      estimated_hours: 12,
      requirements: ['Manage 5+ team members', 'Complete leadership training'],
      badge_icon: '👑',
      points_awarded: 500
    },
    {
      id: 'revenue-optimization',
      title: 'Revenue Optimization Certification',
      description: 'Master revenue sharing and financial optimization strategies',
      skill_area: 'finance',
      difficulty_level: 'advanced',
      estimated_hours: 10,
      requirements: ['Generate $1000+ revenue', 'Complete financial training'],
      badge_icon: '💰',
      points_awarded: 400
    }
  ];

  const startCertification = async (certId) => {
    try {
      const { error } = await supabase
        .from('user_certifications')
        .insert({
          user_id: currentUser.id,
          certification_id: certId,
          status: 'in_progress',
          started_at: new Date().toISOString(),
          progress_percentage: 0
        });

      if (error) throw error;

      toast.success('Certification started!');
      loadCertifications();

    } catch (error) {
      console.error('Error starting certification:', error);
      toast.error('Failed to start certification');
    }
  };

  const getUserCertStatus = (certId) => {
    return userCertifications.find(uc => uc.certification_id === certId);
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'danger';
      default: return 'default';
    }
  };

  const getSkillAreaColor = (area) => {
    switch (area) {
      case 'platform': return 'primary';
      case 'management': return 'secondary';
      case 'leadership': return 'warning';
      case 'finance': return 'success';
      default: return 'default';
    }
  };

  const getStatusIcon = (cert) => {
    const userCert = getUserCertStatus(cert.id);
    if (!userCert) return <Play size={16} />;
    if (userCert.status === 'completed') return <CheckCircle size={16} className="text-green-500" />;
    return <Clock size={16} className="text-yellow-500" />;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Skill Certifications</h2>
        <p className="text-white/70">Earn certifications to validate your skills and expertise</p>
      </div>

      {/* User's earned certifications */}
      {userCertifications.filter(uc => uc.status === 'completed').length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Your Certifications</h3>
          <div className="flex flex-wrap gap-4">
            {userCertifications
              .filter(uc => uc.status === 'completed')
              .map(userCert => {
                const cert = certifications.find(c => c.id === userCert.certification_id);
                if (!cert) return null;
                
                return (
                  <Card key={userCert.id} className="w-48">
                    <CardBody className="text-center p-4">
                      <div className="text-3xl mb-2">{cert.badge_icon}</div>
                      <h4 className="font-medium text-sm">{cert.title}</h4>
                      <p className="text-xs text-gray-500 mt-1">
                        Earned {new Date(userCert.completed_at).toLocaleDateString()}
                      </p>
                    </CardBody>
                  </Card>
                );
              })}
          </div>
        </div>
      )}

      {/* Available certifications */}
      <div className="grid gap-4">
        {certifications.map((cert) => {
          const userCert = getUserCertStatus(cert.id);
          const isCompleted = userCert?.status === 'completed';
          const isInProgress = userCert?.status === 'in_progress';
          const progressPercentage = userCert?.progress_percentage || 0;

          return (
            <Card key={cert.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-start space-x-3">
                    <div className="text-3xl">{cert.badge_icon}</div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-1">{cert.title}</h3>
                      <p className="text-gray-600 text-sm">{cert.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      color={getDifficultyColor(cert.difficulty_level)} 
                      variant="flat" 
                      size="sm"
                    >
                      {cert.difficulty_level}
                    </Badge>
                    <Badge 
                      color={getSkillAreaColor(cert.skill_area)} 
                      variant="flat" 
                      size="sm"
                    >
                      {cert.skill_area}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardBody className="pt-2">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>⏱️ {cert.estimated_hours} hours</span>
                    <span>🏆 {cert.points_awarded} points</span>
                  </div>
                  {getStatusIcon(cert)}
                </div>

                {isInProgress && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{progressPercentage}%</span>
                    </div>
                    <Progress value={progressPercentage} color="primary" size="sm" />
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="flat"
                    onClick={() => {
                      setSelectedCert(cert);
                      setShowCertModal(true);
                    }}
                  >
                    View Details
                  </Button>
                  
                  <Button
                    color={isCompleted ? "success" : "primary"}
                    variant={isCompleted ? "flat" : "solid"}
                    size="sm"
                    startContent={getStatusIcon(cert)}
                    onClick={() => startCertification(cert.id)}
                    disabled={isCompleted}
                  >
                    {isCompleted ? 'Completed' : isInProgress ? 'Continue' : 'Start Certification'}
                  </Button>
                </div>
              </CardBody>
            </Card>
          );
        })}
      </div>

      {/* Certification Details Modal */}
      <Modal 
        isOpen={showCertModal} 
        onClose={() => setShowCertModal(false)}
        size="2xl"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center space-x-3">
              <span className="text-3xl">{selectedCert?.badge_icon}</span>
              <h3>{selectedCert?.title}</h3>
            </div>
          </ModalHeader>
          <ModalBody>
            <p className="mb-4">{selectedCert?.description}</p>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Requirements:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {selectedCert?.requirements?.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </div>
              
              <div className="flex items-center space-x-6 text-sm">
                <span>⏱️ Duration: {selectedCert?.estimated_hours} hours</span>
                <span>🏆 Points: {selectedCert?.points_awarded}</span>
                <span>📊 Level: {selectedCert?.difficulty_level}</span>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onClick={() => setShowCertModal(false)}>
              Close
            </Button>
            <Button 
              color="primary"
              onClick={() => {
                startCertification(selectedCert.id);
                setShowCertModal(false);
              }}
            >
              Start Certification
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SkillCertification;
