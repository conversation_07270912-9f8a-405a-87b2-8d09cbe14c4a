import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Badge, Tabs, Tab, Textarea } from '@heroui/react';
import { CheckCircle, XCircle, Clock, Eye, MessageSquare } from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const ApprovalWorkflow = () => {
  const { currentUser } = useAuth();
  const [approvals, setApprovals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('pending');
  const [reviewComment, setReviewComment] = useState('');
  const [reviewingItem, setReviewingItem] = useState(null);

  useEffect(() => {
    loadApprovals();
  }, [currentUser, selectedTab]);

  const loadApprovals = async () => {
    try {
      setLoading(true);

      let query = supabase
        .from('approval_requests')
        .select(`
          *,
          projects (
            id,
            name
          ),
          users (
            id,
            display_name,
            avatar_url
          )
        `);

      // Filter based on selected tab
      if (selectedTab === 'pending') {
        query = query.eq('status', 'pending');
      } else if (selectedTab === 'my-requests') {
        query = query.eq('requester_id', currentUser.id);
      } else if (selectedTab === 'my-reviews') {
        query = query.eq('reviewer_id', currentUser.id);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;

      setApprovals(data || []);

    } catch (error) {
      console.error('Error loading approvals:', error);
      toast.error('Failed to load approval requests');
      // Fallback to mock data
      setApprovals(getMockApprovals());
    } finally {
      setLoading(false);
    }
  };

  const getMockApprovals = () => [
    {
      id: 'approval-1',
      request_type: 'project_milestone',
      title: 'Complete Alpha Version',
      description: 'Request approval for completing the alpha version milestone',
      status: 'pending',
      priority: 'high',
      requester_id: 'user-1',
      reviewer_id: currentUser.id,
      project_id: 'proj-1',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      users: {
        id: 'user-1',
        display_name: 'John Developer',
        avatar_url: null
      },
      projects: {
        id: 'proj-1',
        name: 'Alpha Development Project'
      }
    },
    {
      id: 'approval-2',
      request_type: 'budget_increase',
      title: 'Budget Increase Request',
      description: 'Request additional budget for development resources',
      status: 'pending',
      priority: 'medium',
      requester_id: 'user-2',
      reviewer_id: currentUser.id,
      project_id: 'proj-2',
      created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      due_date: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
      users: {
        id: 'user-2',
        display_name: 'Sarah Manager',
        avatar_url: null
      },
      projects: {
        id: 'proj-2',
        name: 'Beta Testing Project'
      }
    }
  ];

  const handleApproval = async (approvalId, decision, comment = '') => {
    try {
      const { error } = await supabase
        .from('approval_requests')
        .update({
          status: decision,
          reviewer_comment: comment,
          reviewed_at: new Date().toISOString(),
          reviewer_id: currentUser.id
        })
        .eq('id', approvalId);

      if (error) throw error;

      toast.success(`Request ${decision === 'approved' ? 'approved' : 'rejected'} successfully`);
      setReviewingItem(null);
      setReviewComment('');
      loadApprovals();

    } catch (error) {
      console.error('Error processing approval:', error);
      toast.error('Failed to process approval');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getRequestTypeIcon = (type) => {
    switch (type) {
      case 'project_milestone': return '🎯';
      case 'budget_increase': return '💰';
      case 'team_addition': return '👥';
      case 'scope_change': return '📋';
      default: return '📄';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Approval Workflow</h2>
        <p className="text-white/70">Manage approval requests and review processes</p>
      </div>

      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={setSelectedTab}
        className="mb-6"
      >
        <Tab key="pending" title="Pending Reviews" />
        <Tab key="my-requests" title="My Requests" />
        <Tab key="my-reviews" title="My Reviews" />
      </Tabs>

      {approvals.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <CheckCircle size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Approval Requests</h3>
            <p className="text-gray-600">
              {selectedTab === 'pending' ? 'No pending approvals to review.' : 
               selectedTab === 'my-requests' ? 'You haven\'t submitted any requests.' :
               'No reviews assigned to you.'}
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {approvals.map((approval) => (
            <Card key={approval.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">{getRequestTypeIcon(approval.request_type)}</div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold">{approval.title}</h3>
                      <p className="text-gray-600 text-sm">{approval.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      color={getPriorityColor(approval.priority)} 
                      variant="flat" 
                      size="sm"
                    >
                      {approval.priority}
                    </Badge>
                    <Badge 
                      color={getStatusColor(approval.status)} 
                      variant="flat" 
                      size="sm"
                    >
                      {approval.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardBody className="pt-2">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>👤 {approval.users?.display_name}</span>
                    <span>📋 {approval.projects?.name}</span>
                    <span>📅 {new Date(approval.created_at).toLocaleDateString()}</span>
                    {approval.due_date && (
                      <span>⏰ Due: {new Date(approval.due_date).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>

                {approval.status === 'pending' && approval.reviewer_id === currentUser.id && (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="flat"
                      startContent={<Eye size={14} />}
                      onClick={() => setReviewingItem(approval.id)}
                    >
                      Review
                    </Button>
                  </div>
                )}

                {reviewingItem === approval.id && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <Textarea
                      placeholder="Add your review comments..."
                      value={reviewComment}
                      onChange={(e) => setReviewComment(e.target.value)}
                      className="mb-4"
                    />
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        color="success"
                        startContent={<CheckCircle size={14} />}
                        onClick={() => handleApproval(approval.id, 'approved', reviewComment)}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        startContent={<XCircle size={14} />}
                        onClick={() => handleApproval(approval.id, 'rejected', reviewComment)}
                      >
                        Reject
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        onClick={() => {
                          setReviewingItem(null);
                          setReviewComment('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                {approval.reviewer_comment && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <MessageSquare size={16} className="text-blue-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-blue-700">Review Comment:</p>
                        <p className="text-sm text-blue-600">{approval.reviewer_comment}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ApprovalWorkflow;
