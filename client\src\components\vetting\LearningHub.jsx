import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, Chip, Progress, Input, Select, SelectItem, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { linkedInLearningService } from '../../services/linkedInLearningService';
import youtubeService from '../../services/youtubeService';
import YouTubeCourseCard from '../learning/YouTubeCourseCard';
import VideoSubmissionForm from '../learning/VideoSubmissionForm';
import VideoRecommendationCard from '../learning/VideoRecommendationCard';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Learning Hub Component - Multi-Platform Learning Integration
 *
 * Features:
 * - YouTube video integration with embedded player and progress tracking
 * - LinkedIn Learning integration with course recommendations
 * - Technology-specific learning paths and progression tracking
 * - Skill-based course filtering and search functionality
 * - Progress tracking and completion certificates
 * - Integration with skill verification system
 */
const LearningHub = ({ isOpen, onClose, currentUser, userSkills, onUpdate }) => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('recommended');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  
  const [learningData, setLearningData] = useState({
    recommendedCourses: [],
    youtubeVideos: [],
    videoRecommendations: [],
    learningPaths: [],
    inProgressCourses: [],
    completedCourses: [],
    categories: []
  });

  // Load learning data
  useEffect(() => {
    if (isOpen) {
      loadLearningData();
    }
  }, [isOpen]);

  const loadLearningData = async () => {
    try {
      setLoading(true);

      // Get skill names for recommendations
      const skillNames = userSkills.map(skill => skill.name);

      // Get LinkedIn Learning recommendations
      const linkedInCourses = await linkedInLearningService.getRecommendedCourses(skillNames);

      // Get YouTube video recommendations
      const youtubeVideos = await youtubeService.getRecommendedVideos(skillNames);

      // Get community video recommendations
      const { data: videoRecommendations } = await supabase
        .from('video_recommendations')
        .select(`
          *,
          recommended_by_name:auth.users!recommended_by(user_metadata)
        `)
        .eq('is_public', true)
        .order('upvotes', { ascending: false })
        .limit(20);
      
      // Mock learning paths data
      const learningPaths = [
        {
          id: 'path_1',
          title: 'Frontend Development Mastery',
          description: 'Complete path from beginner to expert in modern frontend development',
          courses: 8,
          duration: '40 hours',
          difficulty: 'Intermediate',
          skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
          progress: 25,
          category: 'Development'
        },
        {
          id: 'path_2',
          title: 'Full-Stack JavaScript',
          description: 'Master both frontend and backend JavaScript development',
          courses: 12,
          duration: '60 hours',
          difficulty: 'Advanced',
          skills: ['Node.js', 'React', 'MongoDB', 'Express'],
          progress: 0,
          category: 'Development'
        },
        {
          id: 'path_3',
          title: 'UI/UX Design Fundamentals',
          description: 'Learn the principles of user interface and experience design',
          courses: 6,
          duration: '30 hours',
          difficulty: 'Beginner',
          skills: ['Figma', 'Design Systems', 'User Research'],
          progress: 60,
          category: 'Design'
        }
      ];

      // Mock in-progress and completed courses
      const inProgressCourses = [
        {
          id: 'course_1',
          title: 'Advanced React Patterns',
          provider: 'LinkedIn Learning',
          duration: '4 hours',
          progress: 75,
          category: 'Development',
          instructor: 'Eve Porcello'
        },
        {
          id: 'course_2',
          title: 'TypeScript Essential Training',
          provider: 'LinkedIn Learning',
          duration: '3 hours',
          progress: 40,
          category: 'Development',
          instructor: 'Jess Chadwick'
        }
      ];

      const completedCourses = [
        {
          id: 'course_3',
          title: 'React.js Essential Training',
          provider: 'LinkedIn Learning',
          duration: '5 hours',
          completedAt: new Date('2025-01-10'),
          category: 'Development',
          certificate: true
        }
      ];

      const categories = [
        'Development',
        'Design',
        'Data Science',
        'Project Management',
        'Marketing',
        'Business'
      ];

      setLearningData({
        recommendedCourses: linkedInCourses,
        youtubeVideos,
        videoRecommendations: videoRecommendations || [],
        learningPaths,
        inProgressCourses,
        completedCourses,
        categories
      });
      
    } catch (error) {
      console.error('Error loading learning data:', error);
      toast.error('Failed to load learning data');
    } finally {
      setLoading(false);
    }
  };

  // Handle course enrollment
  const handleEnrollCourse = async (courseId) => {
    try {
      toast.success('Enrolled in course successfully!');
      // Refresh data
      loadLearningData();
    } catch (error) {
      console.error('Error enrolling in course:', error);
      toast.error('Failed to enroll in course');
    }
  };

  // Handle learning path start
  const handleStartLearningPath = async (pathId) => {
    try {
      toast.success('Learning path started successfully!');
      // Refresh data
      loadLearningData();
    } catch (error) {
      console.error('Error starting learning path:', error);
      toast.error('Failed to start learning path');
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    const colors = {
      'Beginner': 'success',
      'Intermediate': 'warning',
      'Advanced': 'danger'
    };
    return colors[difficulty] || 'default';
  };

  // Filter courses based on search and category
  const filterCourses = (courses) => {
    return courses.filter(course => {
      const matchesSearch = !searchTerm ||
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  // Filter YouTube videos based on search and category
  const filterVideos = (videos) => {
    return videos.filter(video => {
      const matchesSearch = !searchTerm ||
        video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.channelTitle?.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  };

  // Handle YouTube video enrollment
  const handleVideoEnroll = async (video) => {
    try {
      // Add video to course catalog
      await youtubeService.addToCatalog(video);

      // Track enrollment
      await youtubeService.trackProgress(currentUser.id, video.id, {
        title: video.title,
        percentage: 0,
        timeSpent: 0
      });

      toast.success(`Enrolled in "${video.title}"`);

      // Refresh learning data
      loadLearningData();

    } catch (error) {
      console.error('Error enrolling in video:', error);
      toast.error('Failed to enroll in video');
    }
  };

  // Handle video progress updates
  const handleVideoProgress = async (progressData) => {
    try {
      await youtubeService.trackProgress(currentUser.id, progressData.videoId, progressData);
    } catch (error) {
      console.error('Error tracking video progress:', error);
    }
  };

  // Handle video completion
  const handleVideoComplete = async (completionData) => {
    try {
      await youtubeService.trackProgress(currentUser.id, completionData.videoId, {
        percentage: 100,
        completed_at: completionData.completedAt,
        timeSpent: Math.round(completionData.totalTime / 60)
      });

      toast.success('Video completed! 🎉');

      // Refresh learning data to update progress
      loadLearningData();

    } catch (error) {
      console.error('Error tracking video completion:', error);
    }
  };

  // Handle video submission success
  const handleVideoSubmissionSuccess = (submission) => {
    toast.success('Thank you for your submission! It will be reviewed by our team.');
    // Optionally refresh data or show submission in pending state
  };

  // Handle video recommendation vote
  const handleRecommendationVote = (recommendationId, voteType) => {
    // Update local state to reflect vote change
    setLearningData(prev => ({
      ...prev,
      videoRecommendations: prev.videoRecommendations.map(rec =>
        rec.id === recommendationId
          ? {
              ...rec,
              upvotes: voteType === 'upvote' ? (rec.upvotes || 0) + 1 : rec.upvotes,
              downvotes: voteType === 'downvote' ? (rec.downvotes || 0) + 1 : rec.downvotes
            }
          : rec
      )
    }));
  };

  // Render recommended courses tab
  const renderRecommendedTab = () => (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          placeholder="Search courses..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startContent={<span className="text-default-400">🔍</span>}
        />
        
        <Select
          placeholder="Category"
          selectedKeys={[selectedCategory]}
          onSelectionChange={(keys) => setSelectedCategory(Array.from(keys)[0])}
        >
          <SelectItem key="all">All Categories</SelectItem>
          {learningData.categories.map((category) => (
            <SelectItem key={category} value={category}>
              {category}
            </SelectItem>
          ))}
        </Select>
      </div>

      {/* YouTube Videos Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">YouTube Learning Videos</h3>
          <Chip color="danger" size="sm" variant="flat">
            {filterVideos(learningData.youtubeVideos).length} videos
          </Chip>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filterVideos(learningData.youtubeVideos).map((video, index) => (
            <YouTubeCourseCard
              key={video.id}
              video={video}
              progress={0} // TODO: Get actual progress from database
              isEnrolled={false} // TODO: Check enrollment status
              onEnroll={handleVideoEnroll}
              onProgress={handleVideoProgress}
              onComplete={handleVideoComplete}
            />
          ))}
        </div>

        {filterVideos(learningData.youtubeVideos).length === 0 && (
          <div className="text-center py-8 text-default-600">
            <i className="bi bi-youtube text-4xl mb-2 block"></i>
            <p>No YouTube videos found matching your criteria</p>
          </div>
        )}
      </div>

      {/* LinkedIn Learning Courses */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">LinkedIn Learning Courses</h3>
          <Chip color="primary" size="sm" variant="flat">
            {filterCourses(learningData.recommendedCourses).length} courses
          </Chip>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filterCourses(learningData.recommendedCourses).map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardBody className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="font-semibold line-clamp-2">{course.title}</h4>
                    <Chip color="primary" size="sm" variant="flat">
                      {course.provider}
                    </Chip>
                  </div>

                  <p className="text-sm text-default-600 mb-3 line-clamp-2">
                    {course.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="text-sm text-default-500">
                      {course.duration} • {course.instructor}
                    </div>
                    <Chip
                      color={getDifficultyColor(course.difficulty)}
                      size="sm"
                      variant="flat"
                    >
                      {course.difficulty}
                    </Chip>
                  </div>

                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                    className="w-full"
                    onClick={() => handleEnrollCourse(course.id)}
                  >
                    Enroll Now
                  </Button>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {filterCourses(learningData.recommendedCourses).length === 0 && (
          <div className="text-center py-8 text-default-600">
            <i className="bi bi-linkedin text-4xl mb-2 block"></i>
            <p>No LinkedIn Learning courses found matching your criteria</p>
          </div>
        )}
      </div>

      {/* Community Video Recommendations */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Community Recommendations</h3>
          <div className="flex items-center gap-2">
            <Chip color="success" size="sm" variant="flat">
              {learningData.videoRecommendations.length} recommendations
            </Chip>
            <Button
              size="sm"
              color="primary"
              variant="flat"
              onClick={() => setShowSubmissionForm(true)}
            >
              <i className="bi bi-plus-circle mr-1"></i>
              Submit Video
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {learningData.videoRecommendations.slice(0, 6).map((recommendation, index) => (
            <VideoRecommendationCard
              key={recommendation.id}
              recommendation={recommendation}
              onVote={handleRecommendationVote}
              onRecommend={() => loadLearningData()} // Refresh data after new recommendation
            />
          ))}
        </div>

        {learningData.videoRecommendations.length === 0 && (
          <div className="text-center py-8 text-default-600">
            <i className="bi bi-people text-4xl mb-2 block"></i>
            <p>No community recommendations yet</p>
            <Button
              size="sm"
              color="primary"
              variant="flat"
              onClick={() => setShowSubmissionForm(true)}
              className="mt-2"
            >
              Be the first to submit a video!
            </Button>
          </div>
        )}
      </div>
    </div>
  );

  // Render learning paths tab
  const renderLearningPathsTab = () => (
    <div className="space-y-4">
      {learningData.learningPaths.map((path, index) => (
        <motion.div
          key={path.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="hover:shadow-lg transition-shadow">
            <CardBody className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-2">{path.title}</h3>
                  <p className="text-default-600 mb-3">{path.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-3">
                    {path.skills.map((skill) => (
                      <Chip key={skill} size="sm" variant="bordered">
                        {skill}
                      </Chip>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-default-500">Courses:</span>
                      <span className="ml-2 font-medium">{path.courses}</span>
                    </div>
                    <div>
                      <span className="text-default-500">Duration:</span>
                      <span className="ml-2 font-medium">{path.duration}</span>
                    </div>
                    <div>
                      <span className="text-default-500">Level:</span>
                      <span className="ml-2 font-medium">{path.difficulty}</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-center ml-6">
                  <div className="text-2xl font-bold text-primary mb-1">
                    {path.progress}%
                  </div>
                  <div className="text-xs text-default-500">Complete</div>
                </div>
              </div>
              
              <Progress 
                value={path.progress} 
                color="primary" 
                size="sm"
                className="mb-4"
              />
              
              <div className="flex gap-2">
                {path.progress === 0 ? (
                  <Button
                    color="primary"
                    onClick={() => handleStartLearningPath(path.id)}
                  >
                    Start Learning Path
                  </Button>
                ) : path.progress < 100 ? (
                  <Button
                    color="success"
                    onClick={() => handleStartLearningPath(path.id)}
                  >
                    Continue Learning
                  </Button>
                ) : (
                  <Button
                    color="default"
                    variant="bordered"
                  >
                    View Certificate
                  </Button>
                )}
                
                <Button
                  color="default"
                  variant="flat"
                >
                  View Details
                </Button>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render progress tab
  const renderProgressTab = () => (
    <div className="space-y-6">
      {/* In Progress Courses */}
      <div>
        <h3 className="text-lg font-semibold mb-4">In Progress ({learningData.inProgressCourses.length})</h3>
        <div className="space-y-3">
          {learningData.inProgressCourses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-semibold">{course.title}</h4>
                      <div className="text-sm text-default-600">
                        {course.provider} • {course.instructor}
                      </div>
                    </div>
                    <Chip color="warning" size="sm" variant="flat">
                      {course.progress}%
                    </Chip>
                  </div>
                  
                  <Progress 
                    value={course.progress} 
                    color="warning" 
                    size="sm"
                    className="mb-3"
                  />
                  
                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                  >
                    Continue Course
                  </Button>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Completed Courses */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Completed ({learningData.completedCourses.length})</h3>
        <div className="space-y-3">
          {learningData.completedCourses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">{course.title}</h4>
                      <div className="text-sm text-default-600">
                        Completed on {course.completedAt.toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {course.certificate && (
                        <Chip color="success" size="sm" variant="flat">
                          📜 Certificate
                        </Chip>
                      )}
                      <Chip color="success" size="sm" variant="flat">
                        ✓ Complete
                      </Chip>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="5xl">
        <ModalContent>
          <ModalBody className="py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-default-600">Loading learning hub...</p>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="5xl"
        scrollBehavior="inside"
        classNames={{
          base: "max-h-[90vh]",
          body: "py-6"
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-2xl font-bold">Learning Hub</h2>
            <p className="text-default-600 font-normal">
              Advance your skills with curated learning paths and multi-platform integration
            </p>
          </ModalHeader>

          <ModalBody>
            <Tabs
              selectedKey={activeTab}
              onSelectionChange={setActiveTab}
              className="mb-6"
            >
              <Tab key="recommended" title="Recommended">
                {renderRecommendedTab()}
              </Tab>
              <Tab key="paths" title="Learning Paths">
                {renderLearningPathsTab()}
              </Tab>
              <Tab key="progress" title="My Progress">
                {renderProgressTab()}
              </Tab>
            </Tabs>
          </ModalBody>

          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Video Submission Form */}
      <VideoSubmissionForm
        isOpen={showSubmissionForm}
        onClose={() => setShowSubmissionForm(false)}
        onSubmissionSuccess={handleVideoSubmissionSuccess}
      />
    </>
  );
};

export default LearningHub;
