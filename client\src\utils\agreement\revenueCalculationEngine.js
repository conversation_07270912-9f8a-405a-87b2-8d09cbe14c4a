/**
 * Revenue Calculation Engine
 * 
 * Flexible calculation engine supporting all revenue sharing models:
 * - Percentage splits (simple revenue sharing)
 * - Tiered commissions (increasing % with performance)
 * - Waterfall distributions (recoup costs first)
 * - Advance recoupment (pay back advances)
 * - Equity participation (dividend distributions)
 * - Performance bonuses (milestone-based payments)
 * - Commission structures (sales-based)
 */

import { INDUSTRY_REVENUE_MODELS } from './revenueModelStructures.js';

// ============================================================================
// REVENUE CALCULATION ENGINE CLASS
// ============================================================================

export class RevenueCalculationEngine {
  constructor() {
    this.calculationHistory = [];
    this.debugMode = false;
  }

  /**
   * Main calculation method - routes to appropriate calculator based on model type
   */
  calculateRevenue(revenueModel, revenueData, contributorData, options = {}) {
    try {
      this.log('Starting revenue calculation', { revenueModel: revenueModel.model_type, revenueData });

      // Validate inputs
      this.validateInputs(revenueModel, revenueData, contributorData);

      // Route to appropriate calculation method
      let result;
      switch (revenueModel.model_type) {
        case 'percentage':
          result = this.calculatePercentageSplit(revenueModel, revenueData, contributorData, options);
          break;
        case 'tiered':
          result = this.calculateTieredCommission(revenueModel, revenueData, contributorData, options);
          break;
        case 'waterfall':
          result = this.calculateWaterfallDistribution(revenueModel, revenueData, contributorData, options);
          break;
        case 'commission':
          result = this.calculateCommissionStructure(revenueModel, revenueData, contributorData, options);
          break;
        case 'equity':
          result = this.calculateEquityDistribution(revenueModel, revenueData, contributorData, options);
          break;
        case 'hybrid':
          result = this.calculateHybridModel(revenueModel, revenueData, contributorData, options);
          break;
        default:
          throw new Error(`Unsupported revenue model type: ${revenueModel.model_type}`);
      }

      // Store calculation in history
      this.calculationHistory.push({
        timestamp: new Date().toISOString(),
        modelType: revenueModel.model_type,
        input: { revenueModel, revenueData, contributorData, options },
        result
      });

      this.log('Revenue calculation completed', result);
      return result;

    } catch (error) {
      this.log('Revenue calculation failed', { error: error.message });
      return {
        success: false,
        error: error.message,
        totalRevenue: 0,
        distributions: [],
        breakdown: {}
      };
    }
  }

  /**
   * Calculate percentage-based revenue splits
   */
  calculatePercentageSplit(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    const rules = revenueModel.calculation_rules;

    // Calculate net revenue after deductions
    const netRevenue = this.calculateNetRevenue(revenueData, config.revenue_calculation);

    // Check minimum payout threshold
    if (netRevenue < (config.minimum_payout_threshold || 0)) {
      return {
        success: true,
        totalRevenue: netRevenue,
        distributions: [],
        breakdown: {
          grossRevenue: revenueData.grossRevenue,
          deductions: revenueData.grossRevenue - netRevenue,
          netRevenue,
          belowThreshold: true,
          threshold: config.minimum_payout_threshold
        }
      };
    }

    // Calculate individual distributions
    const distributions = contributorData.map(contributor => {
      const baseShare = this.calculateContributorShare(contributor, contributorData, rules);
      const revenueShare = netRevenue * (config.revenue_share_percentage / 100) * baseShare;
      
      // Apply maximum payout cap if specified
      const cappedShare = config.maximum_individual_payout 
        ? Math.min(revenueShare, config.maximum_individual_payout)
        : revenueShare;

      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        baseSharePercentage: baseShare * 100,
        revenueSharePercentage: config.revenue_share_percentage * baseShare,
        grossAmount: revenueShare,
        cappedAmount: cappedShare,
        finalAmount: cappedShare,
        breakdown: {
          contributionPoints: contributor.contributionPoints || 0,
          hoursWorked: contributor.hoursWorked || 0,
          taskDifficulty: contributor.taskDifficulty || 1
        }
      };
    });

    return {
      success: true,
      totalRevenue: netRevenue,
      totalDistributed: distributions.reduce((sum, d) => sum + d.finalAmount, 0),
      distributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        deductions: revenueData.grossRevenue - netRevenue,
        netRevenue,
        revenueSharePool: netRevenue * (config.revenue_share_percentage / 100),
        remainingRevenue: netRevenue - distributions.reduce((sum, d) => sum + d.finalAmount, 0)
      }
    };
  }

  /**
   * Calculate tiered commission structure
   */
  calculateTieredCommission(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    const tiers = config.commission_tiers || [];

    const distributions = contributorData.map(contributor => {
      const performanceMetric = this.getPerformanceMetric(contributor, revenueData, config.performance_metrics);
      
      // Calculate commission based on tiers
      let totalCommission = 0;
      let remainingAmount = performanceMetric;
      
      const tierBreakdown = [];

      for (const tier of tiers.sort((a, b) => a.threshold - b.threshold)) {
        if (remainingAmount <= 0) break;

        const tierAmount = Math.min(remainingAmount, 
          tier.threshold === 0 ? remainingAmount : tier.threshold - (performanceMetric - remainingAmount));
        
        const tierCommission = tierAmount * (tier.percentage / 100);
        totalCommission += tierCommission;
        
        tierBreakdown.push({
          tier: tier.description,
          threshold: tier.threshold,
          percentage: tier.percentage,
          amount: tierAmount,
          commission: tierCommission
        });

        remainingAmount -= tierAmount;
      }

      // Apply bonuses
      const bonuses = this.calculateBonuses(contributor, performanceMetric, config.bonus_structures || []);
      const totalBonuses = bonuses.reduce((sum, bonus) => sum + bonus.amount, 0);

      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        performanceMetric,
        baseCommission: totalCommission,
        bonuses,
        totalBonuses,
        finalAmount: totalCommission + totalBonuses,
        breakdown: {
          tiers: tierBreakdown,
          bonuses
        }
      };
    });

    return {
      success: true,
      totalRevenue: revenueData.grossRevenue,
      totalDistributed: distributions.reduce((sum, d) => sum + d.finalAmount, 0),
      distributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        commissionModel: 'tiered',
        totalCommissions: distributions.reduce((sum, d) => sum + d.baseCommission, 0),
        totalBonuses: distributions.reduce((sum, d) => sum + d.totalBonuses, 0)
      }
    };
  }

  /**
   * Calculate waterfall distribution
   */
  calculateWaterfallDistribution(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    const waterfallTiers = config.waterfall_tiers || [];
    
    let remainingRevenue = revenueData.grossRevenue;
    const waterfallBreakdown = [];
    const contributorDistributions = new Map();

    // Initialize contributor distributions
    contributorData.forEach(contributor => {
      contributorDistributions.set(contributor.id, {
        contributorId: contributor.id,
        contributorName: contributor.name,
        totalAmount: 0,
        tierDistributions: []
      });
    });

    // Process each waterfall tier
    for (const tier of waterfallTiers) {
      if (remainingRevenue <= 0) break;

      let tierAmount = 0;
      let tierDescription = '';

      switch (tier.type) {
        case 'expense_recoup':
          tierAmount = Math.min(remainingRevenue, revenueData.expenses || 0);
          tierDescription = `Expense recoupment: $${tierAmount.toFixed(2)}`;
          break;

        case 'cost_recoup':
          const cappedCosts = tier.cap_amount ? Math.min(revenueData.developmentCosts || 0, tier.cap_amount) : (revenueData.developmentCosts || 0);
          tierAmount = Math.min(remainingRevenue, cappedCosts);
          tierDescription = `Cost recoupment: $${tierAmount.toFixed(2)}`;
          break;

        case 'fixed_return':
          tierAmount = Math.min(remainingRevenue, tier.cap_amount || remainingRevenue);
          tierDescription = `Fixed return: $${tierAmount.toFixed(2)}`;
          // Distribute to specific parties (e.g., investors)
          break;

        case 'revenue_share':
          tierAmount = remainingRevenue * (tier.allocation_percentage / 100);
          if (tier.cap_amount) {
            tierAmount = Math.min(tierAmount, tier.cap_amount);
          }
          tierDescription = `Revenue share (${tier.allocation_percentage}%): $${tierAmount.toFixed(2)}`;
          
          // Distribute among contributors
          this.distributeAmongContributors(tierAmount, contributorData, contributorDistributions, tier);
          break;

        case 'remainder':
          tierAmount = remainingRevenue;
          tierDescription = `Remainder: $${tierAmount.toFixed(2)}`;
          break;
      }

      waterfallBreakdown.push({
        tierName: tier.name,
        tierType: tier.type,
        description: tierDescription,
        amount: tierAmount,
        remainingAfter: remainingRevenue - tierAmount
      });

      remainingRevenue -= tierAmount;
    }

    // Convert map to array
    const distributions = Array.from(contributorDistributions.values());

    return {
      success: true,
      totalRevenue: revenueData.grossRevenue,
      totalDistributed: distributions.reduce((sum, d) => sum + d.totalAmount, 0),
      distributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        waterfallTiers: waterfallBreakdown,
        remainingRevenue
      }
    };
  }

  /**
   * Calculate commission-based structure (affiliate, sales, etc.)
   */
  calculateCommissionStructure(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    
    const distributions = contributorData.map(contributor => {
      // Get sales/performance data for this contributor
      const salesData = contributor.salesData || {};
      const salesAmount = salesData.totalSales || 0;
      
      // Calculate base commission
      const commissionRate = this.getCommissionRate(salesAmount, config.commission_tiers);
      const baseCommission = salesAmount * (commissionRate / 100);
      
      // Apply performance bonuses
      const bonuses = this.calculatePerformanceBonuses(contributor, salesData, config);
      const totalBonuses = bonuses.reduce((sum, bonus) => sum + bonus.amount, 0);
      
      // Apply deductions (chargebacks, returns, etc.)
      const deductions = this.calculateDeductions(salesData, config);
      const totalDeductions = deductions.reduce((sum, deduction) => sum + deduction.amount, 0);
      
      const finalAmount = Math.max(0, baseCommission + totalBonuses - totalDeductions);

      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        salesAmount,
        commissionRate,
        baseCommission,
        bonuses,
        deductions,
        totalBonuses,
        totalDeductions,
        finalAmount,
        breakdown: {
          salesBreakdown: salesData,
          commissionCalculation: {
            salesAmount,
            rate: commissionRate,
            commission: baseCommission
          }
        }
      };
    });

    return {
      success: true,
      totalRevenue: revenueData.grossRevenue,
      totalDistributed: distributions.reduce((sum, d) => sum + d.finalAmount, 0),
      distributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        totalSales: distributions.reduce((sum, d) => sum + d.salesAmount, 0),
        totalCommissions: distributions.reduce((sum, d) => sum + d.baseCommission, 0),
        totalBonuses: distributions.reduce((sum, d) => sum + d.totalBonuses, 0),
        totalDeductions: distributions.reduce((sum, d) => sum + d.totalDeductions, 0)
      }
    };
  }

  /**
   * Calculate equity-based distributions (dividends, profit sharing)
   */
  calculateEquityDistribution(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    
    // Calculate distributable profit
    const netProfit = this.calculateNetRevenue(revenueData, config.profit_calculation || {});
    const distributionPool = netProfit * (config.distribution_percentage || 100) / 100;

    const distributions = contributorData.map(contributor => {
      const equityPercentage = contributor.equityPercentage || 0;
      const vestedPercentage = this.calculateVestedPercentage(contributor, config.vesting_schedule);
      const effectiveEquity = equityPercentage * (vestedPercentage / 100);
      
      const distributionAmount = distributionPool * (effectiveEquity / 100);

      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        totalEquityPercentage: equityPercentage,
        vestedPercentage,
        effectiveEquityPercentage: effectiveEquity,
        distributionAmount,
        breakdown: {
          vestingDetails: this.getVestingBreakdown(contributor, config.vesting_schedule),
          equityCalculation: {
            totalEquity: equityPercentage,
            vested: vestedPercentage,
            effective: effectiveEquity
          }
        }
      };
    });

    return {
      success: true,
      totalRevenue: revenueData.grossRevenue,
      totalDistributed: distributions.reduce((sum, d) => sum + d.distributionAmount, 0),
      distributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        netProfit,
        distributionPool,
        totalEquityDistributed: distributions.reduce((sum, d) => sum + d.effectiveEquityPercentage, 0)
      }
    };
  }

  /**
   * Calculate hybrid model (combination of multiple models)
   */
  calculateHybridModel(revenueModel, revenueData, contributorData, options) {
    const config = revenueModel.configuration;
    const revenueStreams = config.revenue_streams || [];
    
    const streamResults = [];
    let totalDistributed = 0;
    
    // Calculate each revenue stream
    for (const stream of revenueStreams) {
      const streamModel = {
        model_type: stream.model_type,
        configuration: stream.configuration,
        calculation_rules: stream.calculation_rules || {}
      };
      
      const streamResult = this.calculateRevenue(streamModel, revenueData, contributorData, options);
      
      if (streamResult.success) {
        // Apply stream weight
        const weightedDistributions = streamResult.distributions.map(dist => ({
          ...dist,
          finalAmount: dist.finalAmount * (stream.weight / 100),
          streamName: stream.name,
          streamWeight: stream.weight
        }));
        
        streamResults.push({
          streamName: stream.name,
          streamType: stream.model_type,
          weight: stream.weight,
          distributions: weightedDistributions,
          totalDistributed: weightedDistributions.reduce((sum, d) => sum + d.finalAmount, 0)
        });
        
        totalDistributed += weightedDistributions.reduce((sum, d) => sum + d.finalAmount, 0);
      }
    }
    
    // Combine distributions by contributor
    const combinedDistributions = this.combineStreamDistributions(streamResults, contributorData);

    return {
      success: true,
      totalRevenue: revenueData.grossRevenue,
      totalDistributed,
      distributions: combinedDistributions,
      breakdown: {
        grossRevenue: revenueData.grossRevenue,
        revenueStreams: streamResults,
        hybridModel: true
      }
    };
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Calculate net revenue after deductions
   */
  calculateNetRevenue(revenueData, revenueCalculation) {
    let netRevenue = revenueData.grossRevenue || 0;
    
    if (revenueCalculation && revenueCalculation.deductions) {
      for (const deductionType of revenueCalculation.deductions) {
        const deductionAmount = revenueData.deductions?.[deductionType] || 0;
        
        // Apply deduction caps if specified
        const cap = revenueCalculation.deduction_caps?.[deductionType];
        const cappedDeduction = cap ? Math.min(deductionAmount, netRevenue * (cap / 100)) : deductionAmount;
        
        netRevenue -= cappedDeduction;
      }
    }
    
    return Math.max(0, netRevenue);
  }

  /**
   * Calculate individual contributor's share based on contribution
   */
  calculateContributorShare(contributor, allContributors, rules) {
    if (rules.share_calculation === 'equal') {
      return 1 / allContributors.length;
    }
    
    if (rules.share_calculation === 'proportional') {
      const totalPoints = allContributors.reduce((sum, c) => sum + (c.contributionPoints || 0), 0);
      return totalPoints > 0 ? (contributor.contributionPoints || 0) / totalPoints : 1 / allContributors.length;
    }
    
    if (rules.share_calculation === 'weighted') {
      // Implement weighted calculation based on multiple factors
      const weights = rules.weights || {};
      let contributorScore = 0;
      let totalScore = 0;
      
      for (const c of allContributors) {
        const score = (c.tasksCompleted || 0) * (weights.tasks_completed || 0) +
                     (c.hoursWorked || 0) * (weights.hours_worked || 0) +
                     (c.taskDifficulty || 0) * (weights.task_difficulty || 0);
        
        if (c.id === contributor.id) {
          contributorScore = score;
        }
        totalScore += score;
      }
      
      return totalScore > 0 ? contributorScore / totalScore : 1 / allContributors.length;
    }
    
    return 1 / allContributors.length; // Default to equal split
  }

  /**
   * Get performance metric for tiered calculations
   */
  getPerformanceMetric(contributor, revenueData, performanceMetrics) {
    const metricType = performanceMetrics?.primary_metric || 'revenue_generated';
    
    switch (metricType) {
      case 'revenue_generated':
        return contributor.revenueGenerated || 0;
      case 'units_sold':
        return contributor.unitsSold || 0;
      case 'leads_converted':
        return contributor.leadsConverted || 0;
      default:
        return contributor.performanceValue || 0;
    }
  }

  /**
   * Calculate bonuses based on performance
   */
  calculateBonuses(contributor, performanceMetric, bonusStructures) {
    const bonuses = [];
    
    for (const bonus of bonusStructures) {
      let qualified = false;
      let amount = 0;
      
      switch (bonus.trigger) {
        case 'exceed_target':
          if (performanceMetric >= bonus.target_amount) {
            qualified = true;
            amount = Math.min(
              performanceMetric * (bonus.bonus_percentage / 100),
              bonus.bonus_cap || Infinity
            );
          }
          break;
        // Add other bonus trigger types as needed
      }
      
      if (qualified) {
        bonuses.push({
          name: bonus.name,
          trigger: bonus.trigger,
          amount,
          details: bonus
        });
      }
    }
    
    return bonuses;
  }

  /**
   * Distribute amount among contributors for waterfall tiers
   */
  distributeAmongContributors(amount, contributorData, distributionMap, tier) {
    // Simple equal distribution for now - can be enhanced with contribution weighting
    const perContributor = amount / contributorData.length;
    
    contributorData.forEach(contributor => {
      const distribution = distributionMap.get(contributor.id);
      distribution.totalAmount += perContributor;
      distribution.tierDistributions.push({
        tierName: tier.name,
        amount: perContributor
      });
    });
  }

  /**
   * Get commission rate based on sales amount and tiers
   */
  getCommissionRate(salesAmount, tiers) {
    if (!tiers || tiers.length === 0) return 0;
    
    // Find the highest tier that the sales amount qualifies for
    const qualifyingTiers = tiers.filter(tier => salesAmount >= tier.threshold);
    if (qualifyingTiers.length === 0) return 0;
    
    return qualifyingTiers.sort((a, b) => b.threshold - a.threshold)[0].percentage;
  }

  /**
   * Calculate performance bonuses for commission structures
   */
  calculatePerformanceBonuses(contributor, salesData, config) {
    // Implementation for performance bonuses in commission structures
    return [];
  }

  /**
   * Calculate deductions (chargebacks, returns, etc.)
   */
  calculateDeductions(salesData, config) {
    // Implementation for calculating deductions
    return [];
  }

  /**
   * Calculate vested percentage for equity
   */
  calculateVestedPercentage(contributor, vestingSchedule) {
    // Implementation for vesting calculations
    return 100; // Simplified - assume fully vested
  }

  /**
   * Get vesting breakdown details
   */
  getVestingBreakdown(contributor, vestingSchedule) {
    // Implementation for detailed vesting breakdown
    return {};
  }

  /**
   * Combine distributions from multiple revenue streams
   */
  combineStreamDistributions(streamResults, contributorData) {
    const combinedMap = new Map();
    
    // Initialize with contributor data
    contributorData.forEach(contributor => {
      combinedMap.set(contributor.id, {
        contributorId: contributor.id,
        contributorName: contributor.name,
        totalAmount: 0,
        streamBreakdown: []
      });
    });
    
    // Add distributions from each stream
    streamResults.forEach(stream => {
      stream.distributions.forEach(dist => {
        const combined = combinedMap.get(dist.contributorId);
        if (combined) {
          combined.totalAmount += dist.finalAmount;
          combined.streamBreakdown.push({
            streamName: stream.streamName,
            streamType: stream.streamType,
            amount: dist.finalAmount,
            weight: stream.weight
          });
        }
      });
    });
    
    return Array.from(combinedMap.values());
  }

  /**
   * Validate calculation inputs
   */
  validateInputs(revenueModel, revenueData, contributorData) {
    if (!revenueModel || !revenueModel.model_type) {
      throw new Error('Revenue model and model_type are required');
    }

    if (!revenueData || typeof revenueData.grossRevenue !== 'number') {
      throw new Error('Revenue data with grossRevenue is required');
    }

    if (revenueData.grossRevenue < 0) {
      throw new Error('Gross revenue cannot be negative');
    }

    if (!Array.isArray(contributorData) || contributorData.length === 0) {
      throw new Error('Contributor data array is required');
    }

    // Validate each contributor has required fields
    contributorData.forEach((contributor, index) => {
      if (!contributor.id && !contributor.email) {
        throw new Error(`Contributor at index ${index} must have an id or email`);
      }
      if (!contributor.name && !contributor.display_name) {
        throw new Error(`Contributor at index ${index} must have a name or display_name`);
      }
      // Ensure numeric fields are valid if present
      if (contributor.contributionPoints !== undefined && (typeof contributor.contributionPoints !== 'number' || contributor.contributionPoints < 0)) {
        throw new Error(`Contributor ${contributor.name || contributor.display_name} has invalid contribution points`);
      }
      if (contributor.hoursWorked !== undefined && (typeof contributor.hoursWorked !== 'number' || contributor.hoursWorked < 0)) {
        throw new Error(`Contributor ${contributor.name || contributor.display_name} has invalid hours worked`);
      }
    });
  }

  /**
   * Logging utility
   */
  log(message, data = {}) {
    if (this.debugMode) {
      console.log(`[RevenueCalculationEngine] ${message}`, data);
    }
  }

  /**
   * Enable/disable debug mode
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
  }

  /**
   * Get calculation history
   */
  getCalculationHistory() {
    return this.calculationHistory;
  }

  /**
   * Clear calculation history
   */
  clearHistory() {
    this.calculationHistory = [];
  }

  /**
   * Test the revenue calculation with sample data
   * Useful for demos and validation
   */
  static testCalculation() {
    const engine = new RevenueCalculationEngine();
    engine.setDebugMode(true);

    // Sample revenue model
    const revenueModel = {
      model_type: 'percentage',
      configuration: {
        revenue_share_percentage: 70,
        minimum_payout_threshold: 100,
        contribution_tracking: {
          method: 'points_based',
          weights: {
            tasks_completed: 30,
            hours_worked: 30,
            task_difficulty: 40
          }
        }
      },
      calculation_rules: {
        share_calculation: 'proportional'
      }
    };

    // Sample revenue data
    const revenueData = {
      grossRevenue: 10000,
      deductions: 1000
    };

    // Sample contributor data
    const contributorData = [
      {
        id: '1',
        name: 'Alice Developer',
        contributionPoints: 100,
        hoursWorked: 40,
        taskDifficulty: 4
      },
      {
        id: '2',
        name: 'Bob Designer',
        contributionPoints: 80,
        hoursWorked: 30,
        taskDifficulty: 3
      },
      {
        id: '3',
        name: 'Charlie Manager',
        contributionPoints: 60,
        hoursWorked: 20,
        taskDifficulty: 2
      }
    ];

    try {
      const result = engine.calculateRevenue(revenueModel, revenueData, contributorData);
      console.log('✅ Revenue calculation test passed:', result);
      return result;
    } catch (error) {
      console.error('❌ Revenue calculation test failed:', error);
      return null;
    }
  }
}

// Export the calculation engine
export const revenueCalculationEngine = new RevenueCalculationEngine();
