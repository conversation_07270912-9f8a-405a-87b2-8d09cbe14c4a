import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Slider,
  Textarea,
  Select,
  SelectItem,
  Chip,
  Progress,
  Divider,
  RadioGroup,
  Radio
} from '@heroui/react';
import { 
  Target, 
  Brain, 
  Clock, 
  Zap, 
  Award, 
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Save
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const DifficultyAssessment = ({ taskId, onAssessmentComplete }) => {
  const { user } = useAuth();
  const [assessment, setAssessment] = useState({
    technical_complexity: 5,
    time_investment: 5,
    skill_requirement: 5,
    problem_solving: 5,
    creativity_needed: 5,
    collaboration_level: 5,
    learning_curve: 5,
    risk_factor: 5,
    impact_score: 5,
    overall_difficulty: 5,
    justification: '',
    assessment_type: 'self',
    confidence_level: 'medium'
  });
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [existingAssessment, setExistingAssessment] = useState(null);

  useEffect(() => {
    if (taskId) {
      fetchTaskAndAssessment();
    }
  }, [taskId]);

  const fetchTaskAndAssessment = async () => {
    try {
      setLoading(true);

      // Fetch task details
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError) throw taskError;
      setTask(taskData);

      // Fetch existing assessment if any
      const { data: assessmentData, error: assessmentError } = await supabase
        .from('difficulty_assessments')
        .select('*')
        .eq('task_id', taskId)
        .eq('assessor_id', user.id)
        .single();

      if (assessmentData) {
        setExistingAssessment(assessmentData);
        setAssessment(prev => ({
          ...prev,
          ...assessmentData
        }));
      }

    } catch (error) {
      if (error.code !== 'PGRST116') { // Not found error is expected for new assessments
        console.error('Error fetching task and assessment:', error);
        toast.error('Failed to load task details');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSliderChange = (field, value) => {
    setAssessment(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInputChange = (field, value) => {
    setAssessment(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateOverallScore = () => {
    const weights = {
      technical_complexity: 0.15,
      time_investment: 0.12,
      skill_requirement: 0.15,
      problem_solving: 0.13,
      creativity_needed: 0.10,
      collaboration_level: 0.08,
      learning_curve: 0.12,
      risk_factor: 0.10,
      impact_score: 0.05
    };

    let weightedSum = 0;
    Object.entries(weights).forEach(([key, weight]) => {
      weightedSum += assessment[key] * weight;
    });

    return Math.round(weightedSum * 10) / 10;
  };

  const getDifficultyLevel = (score) => {
    if (score <= 2) return { label: 'Very Easy', color: 'success', icon: CheckCircle };
    if (score <= 4) return { label: 'Easy', color: 'primary', icon: CheckCircle };
    if (score <= 6) return { label: 'Medium', color: 'warning', icon: Target };
    if (score <= 8) return { label: 'Hard', color: 'danger', icon: AlertTriangle };
    return { label: 'Very Hard', color: 'danger', icon: AlertTriangle };
  };

  const handleSaveAssessment = async () => {
    if (!assessment.justification.trim()) {
      toast.error('Please provide a justification for your assessment');
      return;
    }

    setSaving(true);

    try {
      const overallScore = calculateOverallScore();
      const assessmentData = {
        ...assessment,
        overall_difficulty: overallScore,
        task_id: taskId,
        assessor_id: user.id,
        updated_at: new Date().toISOString()
      };

      let result;
      if (existingAssessment) {
        // Update existing assessment
        result = await supabase
          .from('difficulty_assessments')
          .update(assessmentData)
          .eq('id', existingAssessment.id);
      } else {
        // Create new assessment
        assessmentData.created_at = new Date().toISOString();
        result = await supabase
          .from('difficulty_assessments')
          .insert([assessmentData]);
      }

      if (result.error) throw result.error;

      toast.success('Assessment saved successfully!');
      
      if (onAssessmentComplete) {
        onAssessmentComplete(overallScore);
      }

    } catch (error) {
      console.error('Error saving assessment:', error);
      toast.error('Failed to save assessment');
    } finally {
      setSaving(false);
    }
  };

  const assessmentCriteria = [
    {
      key: 'technical_complexity',
      label: 'Technical Complexity',
      description: 'How technically challenging is this task?',
      icon: Brain,
      color: 'primary'
    },
    {
      key: 'time_investment',
      label: 'Time Investment',
      description: 'How much time will this task require?',
      icon: Clock,
      color: 'warning'
    },
    {
      key: 'skill_requirement',
      label: 'Skill Requirement',
      description: 'What level of expertise is needed?',
      icon: Award,
      color: 'success'
    },
    {
      key: 'problem_solving',
      label: 'Problem Solving',
      description: 'How much analytical thinking is required?',
      icon: Target,
      color: 'secondary'
    },
    {
      key: 'creativity_needed',
      label: 'Creativity Needed',
      description: 'How much creative thinking is involved?',
      icon: Zap,
      color: 'primary'
    },
    {
      key: 'collaboration_level',
      label: 'Collaboration Level',
      description: 'How much teamwork is required?',
      icon: TrendingUp,
      color: 'success'
    },
    {
      key: 'learning_curve',
      label: 'Learning Curve',
      description: 'How much new learning is required?',
      icon: Brain,
      color: 'warning'
    },
    {
      key: 'risk_factor',
      label: 'Risk Factor',
      description: 'What is the risk of failure or complications?',
      icon: AlertTriangle,
      color: 'danger'
    },
    {
      key: 'impact_score',
      label: 'Impact Score',
      description: 'How significant is the impact of this task?',
      icon: Target,
      color: 'primary'
    }
  ];

  const confidenceLevels = [
    { key: 'low', label: 'Low Confidence', description: 'Not very sure about this assessment' },
    { key: 'medium', label: 'Medium Confidence', description: 'Reasonably confident in this assessment' },
    { key: 'high', label: 'High Confidence', description: 'Very confident in this assessment' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-white/60">Loading task details...</p>
        </div>
      </div>
    );
  }

  const overallScore = calculateOverallScore();
  const difficultyLevel = getDifficultyLevel(overallScore);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
              <Target size={24} className="text-primary" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Difficulty Assessment</h2>
              <p className="text-white/70">Evaluate the complexity and requirements of this task</p>
            </div>
          </div>
        </CardHeader>
        
        {task && (
          <CardBody className="pt-0">
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="font-semibold text-white mb-2">{task.title}</h3>
              <p className="text-white/70 text-sm">{task.description}</p>
            </div>
          </CardBody>
        )}
      </Card>

      {/* Overall Score Display */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <difficultyLevel.icon size={32} className={`text-${difficultyLevel.color}`} />
              <div>
                <h3 className="text-3xl font-bold text-white">{overallScore}/10</h3>
                <p className="text-white/70">Overall Difficulty</p>
              </div>
            </div>
            <Chip
              color={difficultyLevel.color}
              variant="flat"
              size="lg"
              className="text-lg px-4 py-2"
            >
              {difficultyLevel.label}
            </Chip>
            <Progress
              value={overallScore * 10}
              color={difficultyLevel.color}
              className="w-full max-w-md mx-auto"
            />
          </div>
        </CardBody>
      </Card>

      {/* Assessment Criteria */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <h3 className="text-lg font-semibold text-white">Assessment Criteria</h3>
        </CardHeader>
        
        <CardBody className="pt-0 space-y-6">
          {assessmentCriteria.map((criteria) => (
            <div key={criteria.key} className="space-y-3">
              <div className="flex items-center gap-3">
                <criteria.icon size={20} className={`text-${criteria.color}`} />
                <div className="flex-1">
                  <h4 className="font-medium text-white">{criteria.label}</h4>
                  <p className="text-sm text-white/60">{criteria.description}</p>
                </div>
                <Chip color={criteria.color} variant="flat" size="sm">
                  {assessment[criteria.key]}/10
                </Chip>
              </div>
              <Slider
                value={assessment[criteria.key]}
                onChange={(value) => handleSliderChange(criteria.key, value)}
                minValue={1}
                maxValue={10}
                step={1}
                color={criteria.color}
                className="w-full"
                marks={[
                  { value: 1, label: '1' },
                  { value: 5, label: '5' },
                  { value: 10, label: '10' }
                ]}
              />
            </div>
          ))}
        </CardBody>
      </Card>

      {/* Assessment Details */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <h3 className="text-lg font-semibold text-white">Assessment Details</h3>
        </CardHeader>
        
        <CardBody className="pt-0 space-y-6">
          {/* Assessment Type */}
          <div className="space-y-3">
            <label className="text-white/70 text-sm font-medium">Assessment Type</label>
            <RadioGroup
              value={assessment.assessment_type}
              onValueChange={(value) => handleInputChange('assessment_type', value)}
              orientation="horizontal"
            >
              <Radio value="self" classNames={{ base: "text-white/80" }}>
                Self Assessment
              </Radio>
              <Radio value="peer" classNames={{ base: "text-white/80" }}>
                Peer Review
              </Radio>
              <Radio value="manager" classNames={{ base: "text-white/80" }}>
                Manager Review
              </Radio>
            </RadioGroup>
          </div>

          {/* Confidence Level */}
          <div className="space-y-3">
            <label className="text-white/70 text-sm font-medium">Confidence Level</label>
            <Select
              value={assessment.confidence_level}
              onChange={(e) => handleInputChange('confidence_level', e.target.value)}
              variant="bordered"
              classNames={{
                value: "text-white"
              }}
            >
              {confidenceLevels.map((level) => (
                <SelectItem key={level.key} value={level.key}>
                  <div>
                    <p className="font-medium">{level.label}</p>
                    <p className="text-xs text-white/60">{level.description}</p>
                  </div>
                </SelectItem>
              ))}
            </Select>
          </div>

          {/* Justification */}
          <Textarea
            label="Assessment Justification"
            placeholder="Explain your reasoning for this difficulty assessment..."
            value={assessment.justification}
            onChange={(e) => handleInputChange('justification', e.target.value)}
            isRequired
            variant="bordered"
            minRows={4}
            classNames={{
              input: "text-white",
              label: "text-white/70"
            }}
          />

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button
              color="primary"
              size="lg"
              startContent={<Save size={20} />}
              onPress={handleSaveAssessment}
              isLoading={saving}
              className="min-w-[150px]"
            >
              {saving ? 'Saving...' : existingAssessment ? 'Update Assessment' : 'Save Assessment'}
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default DifficultyAssessment;
