import React from "react";
import { Input as HeroUIInput } from "@heroui/react";
import { cn } from "../../../lib/utils";

/**
 * Input Component - HeroUI Implementation
 *
 * A form input component for collecting user input.
 * Compatible with shadcn/ui Input API for easy migration.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.type] - Input type (text, password, email, etc.)
 * @returns {React.ReactElement} - Input component
 */
const Input = React.forwardRef(({ className, type = "text", style, ...props }, ref) => {
  return (
    <HeroUIInput
      ref={ref}
      type={type}
      variant="bordered"
      className={cn("", className)}
      style={{
        ...style,
        color: '#000000',
        WebkitTextFillColor: '#000000',
        caretColor: '#000000',
      }}
      classNames={{
        input: "text-sm !text-black placeholder:text-gray-500 !caret-black",
        inputWrapper: "border-input bg-background hover:border-ring focus-within:border-ring",
        label: "text-foreground",
        description: "text-muted-foreground",
        errorMessage: "text-destructive",
      }}
      {...props}
    />
  );
});

Input.displayName = "Input";

export { Input };
