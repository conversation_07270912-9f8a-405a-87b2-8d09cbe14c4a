import { test, expect } from '@playwright/test';

test.describe('JSX Runtime Fix Verification', () => {
  test('Verify JSX Runtime Error is Resolved', async ({ page }) => {
    console.log('🔍 Testing JSX runtime fix...');
    
    // Capture console logs and errors
    const consoleLogs = [];
    const jsErrors = [];
    
    page.on('console', msg => {
      const logMessage = `${msg.type()}: ${msg.text()}`;
      consoleLogs.push(logMessage);
      console.log(`BROWSER: ${logMessage}`);
      
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    page.on('pageerror', error => {
      const errorMessage = `PAGE ERROR: ${error.message}`;
      jsErrors.push(errorMessage);
      console.log(errorMessage);
    });

    // Navigate to the project wizard
    console.log('📍 Navigating to project wizard...');
    await page.goto('/project/wizard');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Take screenshot for visual verification
    await page.screenshot({ 
      path: 'tests/test-results/jsx-runtime-fix-verification.png',
      fullPage: true 
    });
    
    // Check for specific JSX runtime errors
    const hasJSXRuntimeError = jsErrors.some(error => 
      error.includes('jsxDEV is not a function') ||
      error.includes('React is not defined') ||
      error.includes('jsx') ||
      error.includes('JSX')
    );
    
    console.log('🔍 JavaScript Errors Found:', jsErrors.length);
    jsErrors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
    
    // Check if React components are loading
    const hasReactContent = await page.locator('div[data-testid], div[class*="react"], div[class*="component"]').count() > 0;
    const hasWizardElements = await page.locator('input, button, form').count() > 0;
    
    console.log('📊 Component Loading Status:');
    console.log(`  - React content detected: ${hasReactContent}`);
    console.log(`  - Wizard elements detected: ${hasWizardElements}`);
    console.log(`  - JSX runtime errors: ${hasJSXRuntimeError ? 'YES' : 'NO'}`);
    
    // Verify the fix worked
    expect(hasJSXRuntimeError, 'JSX runtime errors should be resolved').toBe(false);
    
    // Check if the page shows actual wizard content instead of dashboard
    const pageContent = await page.textContent('body');
    const showsDashboard = pageContent.includes('Recent Activity') || pageContent.includes('Quick Actions');
    const showsWizard = pageContent.includes('Create Your Project') || pageContent.includes('Project Name') || pageContent.includes('wizard');
    
    console.log('📄 Page Content Analysis:');
    console.log(`  - Shows dashboard content: ${showsDashboard}`);
    console.log(`  - Shows wizard content: ${showsWizard}`);
    
    // Log final status
    if (!hasJSXRuntimeError && hasWizardElements) {
      console.log('✅ JSX RUNTIME FIX SUCCESSFUL - Components are loading properly!');
    } else if (!hasJSXRuntimeError && !hasWizardElements) {
      console.log('⚠️ JSX runtime fixed but wizard elements not detected - may be routing issue');
    } else {
      console.log('❌ JSX runtime errors still present');
    }
    
    // Save detailed log
    const testResults = {
      timestamp: new Date().toISOString(),
      jsxRuntimeErrorsFound: hasJSXRuntimeError,
      totalJSErrors: jsErrors.length,
      jsErrors: jsErrors,
      reactContentDetected: hasReactContent,
      wizardElementsDetected: hasWizardElements,
      showsDashboard: showsDashboard,
      showsWizard: showsWizard,
      consoleLogs: consoleLogs.slice(-20) // Last 20 logs
    };
    
    console.log('💾 Test Results Summary:', JSON.stringify(testResults, null, 2));
  });
});
