import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner
} from '@heroui/react';
import { Bug, Eye, Calendar, AlertTriangle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';

const MyBugReports = () => {
  const { user } = useAuth();
  const [bugReports, setBugReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBug, setSelectedBug] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  useEffect(() => {
    if (user) {
      fetchBugReports();
    }
  }, [user]);

  const fetchBugReports = async () => {
    try {
      const { data, error } = await supabase
        .from('bug_reports')
        .select('*')
        .eq('reporter_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBugReports(data || []);
    } catch (error) {
      console.error('Error fetching bug reports:', error);
      toast.error('Failed to load bug reports');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'danger';
      case 'in_progress': return 'warning';
      case 'resolved': return 'success';
      case 'closed': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return <AlertTriangle size={16} />;
      case 'in_progress': return <Clock size={16} />;
      case 'resolved': return <CheckCircle size={16} />;
      case 'closed': return <XCircle size={16} />;
      default: return <Bug size={16} />;
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'danger';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleViewDetails = (bug) => {
    setSelectedBug(bug);
    onOpen();
  };

  const columns = [
    { key: 'title', label: 'Title' },
    { key: 'severity', label: 'Severity' },
    { key: 'status', label: 'Status' },
    { key: 'category', label: 'Category' },
    { key: 'created_at', label: 'Reported' },
    { key: 'actions', label: 'Actions' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6"
    >
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-full bg-danger/20 flex items-center justify-center">
                <Bug size={24} className="text-danger" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">My Bug Reports</h2>
                <p className="text-white/70">Track the status of your reported issues</p>
              </div>
            </div>
            <Chip color="primary" variant="flat">
              {bugReports.length} Reports
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {bugReports.length === 0 ? (
            <div className="text-center py-12">
              <Bug size={48} className="text-white/30 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Bug Reports</h3>
              <p className="text-white/60">You haven't reported any bugs yet.</p>
            </div>
          ) : (
            <Table
              aria-label="Bug reports table"
              classNames={{
                wrapper: "bg-transparent",
                th: "bg-white/10 text-white",
                td: "text-white/90"
              }}
            >
              <TableHeader columns={columns}>
                {(column) => (
                  <TableColumn key={column.key}>
                    {column.label}
                  </TableColumn>
                )}
              </TableHeader>
              <TableBody items={bugReports}>
                {(item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="max-w-xs">
                        <p className="font-medium truncate">{item.title}</p>
                        <p className="text-sm text-white/60 truncate">{item.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getSeverityColor(item.severity)}
                        variant="flat"
                        size="sm"
                      >
                        {item.severity}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getStatusColor(item.status)}
                        variant="flat"
                        size="sm"
                        startContent={getStatusIcon(item.status)}
                      >
                        {item.status.replace('_', ' ')}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{item.category || 'Other'}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar size={14} className="text-white/60" />
                        <span className="text-sm">{formatDate(item.created_at)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        startContent={<Eye size={16} />}
                        onPress={() => handleViewDetails(item)}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Bug Details Modal */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="2xl"
        classNames={{
          base: "bg-background/95 backdrop-blur-md",
          header: "border-b border-white/20",
          body: "py-6",
          footer: "border-t border-white/20"
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {selectedBug && getStatusIcon(selectedBug.status)}
                    <span className="text-xl font-bold">{selectedBug?.title}</span>
                  </div>
                  <div className="flex gap-2">
                    <Chip
                      color={selectedBug ? getSeverityColor(selectedBug.severity) : 'default'}
                      variant="flat"
                      size="sm"
                    >
                      {selectedBug?.severity}
                    </Chip>
                    <Chip
                      color={selectedBug ? getStatusColor(selectedBug.status) : 'default'}
                      variant="flat"
                      size="sm"
                    >
                      {selectedBug?.status.replace('_', ' ')}
                    </Chip>
                  </div>
                </div>
              </ModalHeader>
              <ModalBody>
                {selectedBug && (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-white mb-2">Description</h4>
                      <p className="text-white/80">{selectedBug.description}</p>
                    </div>
                    
                    {selectedBug.steps_to_reproduce && (
                      <div>
                        <h4 className="font-semibold text-white mb-2">Steps to Reproduce</h4>
                        <p className="text-white/80 whitespace-pre-line">{selectedBug.steps_to_reproduce}</p>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedBug.expected_behavior && (
                        <div>
                          <h4 className="font-semibold text-white mb-2">Expected Behavior</h4>
                          <p className="text-white/80">{selectedBug.expected_behavior}</p>
                        </div>
                      )}
                      
                      {selectedBug.actual_behavior && (
                        <div>
                          <h4 className="font-semibold text-white mb-2">Actual Behavior</h4>
                          <p className="text-white/80">{selectedBug.actual_behavior}</p>
                        </div>
                      )}
                    </div>
                    
                    <div className="text-sm text-white/60 space-y-1">
                      <p><strong>Category:</strong> {selectedBug.category || 'Other'}</p>
                      <p><strong>Reported:</strong> {formatDate(selectedBug.created_at)}</p>
                      {selectedBug.url && <p><strong>URL:</strong> {selectedBug.url}</p>}
                    </div>
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button color="primary" variant="light" onPress={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </motion.div>
  );
};

export default MyBugReports;
