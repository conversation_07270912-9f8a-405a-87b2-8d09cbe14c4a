import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';

// Helper function to check page content for mock data patterns
async function checkForMockDataPatterns(page, pageName) {
  console.log(`🔍 Checking ${pageName} for mock data patterns...`);

  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);

  // Take screenshot for verification
  await page.screenshot({
    path: `test-results/${pageName}-mock-data-check-${Date.now()}.png`,
    fullPage: true
  });

  // Get page content
  const pageContent = await page.content();

  // Check for mock data patterns
  const mockPatterns = [
    'Mock',
    'mock',
    'fallback calculation',
    'totalProjected * 0.7',
    'totalOpportunities: 47',
    'activeApplications: 12',
    'completedProjects: 8',
    'totalEarnings: 15420',
    'Mock Bank Account',
    '**** 1234',
    'Mock Payment Method',
    'Mock Transaction',
    'Sample payment',
    'Test transaction',
    'Mock exchange rate',
    '1.0 USD = 1.0 EUR',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  const foundMockPatterns = mockPatterns.filter(pattern =>
    pageContent.includes(pattern)
  );

  console.log(`📊 Found ${foundMockPatterns.length} mock data patterns in ${pageName}`);
  if (foundMockPatterns.length > 0) {
    console.log('🚨 Mock patterns found:', foundMockPatterns);
  }

  return foundMockPatterns;
}

test.describe('Production Real Data Integration Tests', () => {

  test('1. Homepage - No Mock Data Patterns', async ({ page }) => {
    console.log('🏠 Testing homepage for mock data elimination...');

    const mockPatterns = await checkForMockDataPatterns(page, 'homepage');

    // Verify no mock data patterns exist
    expect(mockPatterns.length).toBe(0);

    console.log('✅ Homepage passed mock data elimination test');
  });

  test('2. Login Page - Authentication System Verification', async ({ page }) => {
    console.log('🔐 Testing authentication system functionality...');

    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Verify login form is present and functional
    const emailInput = page.locator('input[type="email"], input[name="email"], textbox:has-text("Enter email")');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    const loginButton = page.locator('button:has-text("Log In")');

    // Check that authentication elements are present
    expect(await emailInput.count()).toBeGreaterThan(0);
    expect(await passwordInput.count()).toBeGreaterThan(0);
    expect(await loginButton.count()).toBeGreaterThan(0);

    // Verify OAuth options are available
    const googleAuth = page.locator('button:has-text("Continue with Google")');
    const githubAuth = page.locator('button:has-text("Continue with GitHub")');

    expect(await googleAuth.count()).toBeGreaterThan(0);
    expect(await githubAuth.count()).toBeGreaterThan(0);

    console.log('✅ Authentication system is properly configured');
  });

  test('3. Network Requests - Real API Calls Verification', async ({ page }) => {
    console.log('🌐 Testing network requests for real API integration...');

    const networkRequests = [];

    // Monitor network requests
    page.on('request', request => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      });
    });

    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // Filter for API requests
    const apiRequests = networkRequests.filter(req =>
      req.url.includes('supabase') ||
      req.url.includes('api') ||
      req.url.includes('exchangerate-api') ||
      req.url.includes('teller')
    );

    console.log('📡 Total network requests:', networkRequests.length);
    console.log('🔗 API requests found:', apiRequests.length);
    console.log('📋 API requests:', apiRequests.slice(0, 10));

    // Should have real API requests
    expect(apiRequests.length).toBeGreaterThan(0);

    console.log('✅ Network requests show real API integration');
  });

  test('4. Console Messages - Real Data Loading Verification', async ({ page }) => {
    console.log('📝 Testing console messages for real data loading...');

    const consoleMessages = [];
    const errorMessages = [];

    // Monitor console messages
    page.on('console', msg => {
      consoleMessages.push(msg.text());
      if (msg.type() === 'error') {
        errorMessages.push(msg.text());
      }
    });

    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // Filter for real data related messages
    const realDataMessages = consoleMessages.filter(msg =>
      msg.includes('✅ Loaded real') ||
      msg.includes('Real data') ||
      msg.includes('Database query') ||
      msg.includes('Supabase') ||
      msg.includes('auth') ||
      msg.includes('payment') ||
      msg.includes('escrow') ||
      msg.includes('revenue')
    );

    console.log('📊 Total console messages:', consoleMessages.length);
    console.log('✅ Real data messages:', realDataMessages.length);
    console.log('❌ Error messages:', errorMessages.length);
    console.log('📋 Sample real data messages:', realDataMessages.slice(0, 5));

    // Should have some real data loading activity
    expect(realDataMessages.length).toBeGreaterThan(0);

    // Should not have critical errors
    const criticalErrors = errorMessages.filter(msg =>
      msg.includes('Failed to fetch') ||
      msg.includes('Network error') ||
      msg.includes('500') ||
      msg.includes('404')
    );

    expect(criticalErrors.length).toBe(0);

    console.log('✅ Console messages show real data integration without critical errors');
  });

  test('5. Production Environment Verification', async ({ page }) => {
    console.log('🌐 Testing production environment configuration...');

    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Check that we're actually hitting the production URL
    const currentUrl = page.url();
    expect(currentUrl).toContain('royalty.technology');

    // Check for production indicators
    const pageContent = await page.content();

    // Should not contain development indicators
    expect(pageContent).not.toContain('localhost');
    expect(pageContent).not.toContain('development');
    expect(pageContent).not.toContain('dev-mode');

    // Should not contain placeholder content (but allow form placeholders)
    expect(pageContent).not.toContain('under construction');
    expect(pageContent).not.toContain('coming soon');

    // Verify we have a proper production title
    const title = await page.title();
    expect(title).toContain('Royaltea');

    console.log('✅ Production environment verification passed');
  });

  test('6. Final Mock Data Elimination Verification', async ({ page }) => {
    console.log('🧹 Final comprehensive mock data elimination test...');

    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // Take final screenshot
    await page.screenshot({
      path: `test-results/final-production-verification-${Date.now()}.png`,
      fullPage: true
    });

    const pageContent = await page.content();

    // Check for critical mock data patterns (excluding legitimate UI placeholders)
    const criticalMockPatterns = [
      'Mock',
      'fallback calculation',
      'totalProjected * 0.7',
      'totalOpportunities: 47',
      'activeApplications: 12',
      'completedProjects: 8',
      'totalEarnings: 15420',
      'Mock Bank Account',
      '**** 1234',
      'Mock Payment Method',
      'Mock Transaction',
      'Sample payment',
      'Test transaction',
      'Mock exchange rate',
      '1.0 USD = 1.0 EUR',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      'under construction',
      'coming soon',
      'lorem ipsum',
      'test data',
      'sample data'
    ];

    const foundMockPatterns = criticalMockPatterns.filter(pattern =>
      pageContent.toLowerCase().includes(pattern.toLowerCase())
    );

    console.log('🔍 Critical mock data check results:');
    console.log(`📊 Total patterns checked: ${criticalMockPatterns.length}`);
    console.log(`🚨 Mock patterns found: ${foundMockPatterns.length}`);

    if (foundMockPatterns.length > 0) {
      console.log('❌ Found mock patterns:', foundMockPatterns);
    }

    // Final verification - should have ZERO critical mock data patterns
    expect(foundMockPatterns.length).toBe(0);

    console.log('🎉 PRODUCTION READY: All critical mock data has been eliminated!');
  });
});
